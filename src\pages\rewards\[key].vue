<script setup lang="ts">
import { computed, ref, watchEffect, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Snackbar } from '@varlet/ui'
import { useRewardConfig } from './composables/useRewardConfig'
import { useRewardStatus } from './composables/useRewardStatus'
import RewardItem from './components/RewardItem.vue'
import UnauthorizedDialog from './components/UnauthorizedDialog.vue'

const route = useRoute()
const router = useRouter()
// 确保key有值
const key = computed(() => {
	const routeKey = route.params.key
	return typeof routeKey === 'string' ? routeKey : ''
})

// 获取奖励配置
const {
	config,
	loading: configLoading,
	error: configError,
	refresh: refreshConfig,
} = useRewardConfig(key)

// 奖励状态
const {
	statusMap,
	loading: statusLoading,
	error: statusError,
	claimReward,
	refreshStatus,
} = useRewardStatus(computed(() => config.value?.rewards?.keys || []))

// 未授权弹窗
const showUnauthorizedDialog = ref(false)
const unauthorizedRewardId = ref('')

// 关闭未授权弹窗
const closeUnauthorizedDialog = () => {
	showUnauthorizedDialog.value = false
}

// 点击领取奖励
const handleClaim = async (rewardId: string) => {
	const status = statusMap.value[rewardId]

	if (status === -1) {
		// 未参与活动
		unauthorizedRewardId.value = rewardId
		showUnauthorizedDialog.value = true
		return
	}

	if (status === 1) {
		// 已领取，不做操作
		return
	}

	// 执行领取逻辑并刷新状态
	try {
		// 调用领取接口
		const success = await claimReward(rewardId)
		if (!success) {
			// 领取失败，需要显示提示
			const errorMsg =
				config.value?.rewards?.items.find((item) => item.id === rewardId)
					?.name || '奖励'
			Snackbar.error(`领取${errorMsg}失败，请稍后重试`)
		}
	} catch (error) {
		console.error('领取奖励失败', error)
		Snackbar.error('领取奖励失败，请稍后重试')
	}
}

const isLoading = computed(() => configLoading.value || statusLoading.value)
const hasError = computed(() => configError.value || statusError.value)

// 初始化数据
onMounted(() => {
	if (key.value) {
		refreshConfig()
	}
})
</script>

<template>
	<div
		class="relative min-h-screen w-full p-4 flex flex-col justify-center items-center"
	>
		<!-- 带logo图片的header后退功能 -->
		<div class="h-8 w-full fixed top-0 left-0 flex">
			<!-- icon -->
			<var-button
				color="transparent"
				text-color="black"
				round
				text
				@click="router.back"
			>
				<div class="i-mdi-chevron-left w-6 h-6"></div>
			</var-button>
      <!-- logo -->
      <div class="h-8 flex items-center justify-center">
        <img src="" alt="logo" class="h-full" />
      </div>
		</div>

		<!-- 背景图 -->
		<div class="fixed inset-0 z-[-1]">
			<img
				v-if="config?.backgroundImage"
				:src="config.backgroundImage"
				alt="背景图"
				class="w-full h-full object-cover object-center"
			/>
		</div>

		<!-- 加载状态 -->
		<div
			v-if="isLoading"
			class="flex-1 flex flex-col items-center justify-center gap-4"
		>
			<var-loading type="circle" color="#FFD722" />
			<span class="text-2xl text-black">加载中...</span>
		</div>

		<!-- 错误状态 -->
		<div
			v-else-if="hasError"
			class="flex-1 flex flex-col items-center justify-center gap-4"
		>
			<var-icon name="error_outline" color="#ff4757" size="36" />
			<span class="text-2xl text-black">加载失败，请重试</span>
			<var-button type="primary" @click="refreshConfig">刷新页面</var-button>
		</div>

		<!-- 内容区 -->
		<div v-else class="flex flex-col gap-4 mt-4 w-full">
			<template v-if="config?.rewards?.items?.length">
				<RewardItem
					v-for="item in config.rewards.items"
					:key="item.id"
					:reward="item"
					:status="statusMap[item.id] || 0"
					@claim="handleClaim(item.id)"
				/>
			</template>
			<div
				v-else
				class="flex-1 flex flex-col items-center justify-center gap-3 py-10 text-[#8795a1]"
			>
				<var-icon name="inbox" size="36" />
				<span>暂无可领取奖励</span>
			</div>
		</div>

		<!-- 未授权弹窗 -->
		<UnauthorizedDialog
			v-model:show="showUnauthorizedDialog"
			:reward-id="unauthorizedRewardId"
			@close="closeUnauthorizedDialog"
		/>
	</div>
</template>

<route lang="json">
{
	"meta": {
		"title": "奖励",
		"layout": false
	}
}
</route>
