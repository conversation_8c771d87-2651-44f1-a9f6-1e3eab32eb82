@import './md.css';
@import './reset.css';


html.dark {
	background: #100c2a !important;
}

.prose {
	scroll-behavior: smooth;
}

#nprogress {
	pointer-events: none;
}

#nprogress .bar {
	@apply bg-blue-700 bg-opacity-75;

	background: repeating-linear-gradient(90deg, #00dc82 0, #34cdfe 50%, #0047e1);
	position: fixed;
	z-index: 1031;
	top: 0;
	left: 0;

	width: 100%;
	height: 2px;
}

/*
* 全局滚动条
*/
::-webkit-scrollbar {
	width: 8px;
	height: 11px;
	background-color: rgb(246, 247, 248);
}

::-webkit-scrollbar-thumb {
	background-color: rgb(233, 236, 239);
	border-radius: 10px;
}

::-webkit-scrollbar-track {
	background-color: rgb(246, 247, 248);
	border-radius: 10px;
}

html.dark ::-webkit-scrollbar,
html.dark ::-webkit-scrollbar-track {
	background-color: #212529;
}

html.dark ::-webkit-scrollbar-thumb {
	background-color: #343a40;
}

html, body {
  touch-action: manipulation;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Roboto', 'PingFang SC', 'Helvetica Neue', 'Arial', sans-serif;
}

* {
  /* 防止双击选中文本和缩放 */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
}

img {
  /* 防止长按图片出现菜单 */
  -webkit-user-select: none;
  user-select: none;
}

/* 防止iOS上的文本元素自动放大 */
input, textarea, select, button {
  font-size: 16px;
} 