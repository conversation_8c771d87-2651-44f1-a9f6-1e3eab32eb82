<template>
  <div class="game-list-container" p="x-4 y-4">
    <SearchBox 
			class="mb-10"
      :list="gameList" 
      placeholder="搜索游戏" 
      @update:filtered-list="filteredGameList = $event"
    />
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container" flex justify-center items-center py-12>
      <var-loading type="circle" color="#1989fa" size="large" />
    </div>
    
    <!-- 错误状态 -->
    <div v-else-if="hasError" class="error-container" flex="~ col" items-center justify-center py-12>
      <var-icon name="error" color="#f44336" size="32" />
      <p mt-2 text="gray-600">获取游戏列表失败，请重试</p>
      <var-button type="primary" text round size="small" mt-4 @click="fetchGameList">重新加载</var-button>
    </div>
    
    <!-- 空数据状态 -->
    <div v-else-if="filteredGameList.length === 0" class="empty-container" flex="~ col" items-center justify-center py-12>
      <var-icon name="information" color="#909399" size="32" />
      <p mt-2 text="gray-600">没有找到相关游戏</p>
    </div>
    
    <!-- 游戏列表 -->
    <div v-else class="game-grid" mt-4 grid="~ cols-3 gap-3">
      <div 
        v-for="game in filteredGameList" 
        :key="game.id" 
        class="game-item" 
        flex="~ col" 
        items-center
        @click="handleGameClick(game)"
      >
        <div 
          class="game-image" 
          w-22 h-22 
          flex="~" 
          items-center 
          justify-center 
          rounded-2
          bg="gray-100"
          mb-2
          overflow-hidden
        >
          <img :src="game.image" :alt="game.name" w-full h-full object-cover>
        </div>
        <p class="game-name" text="center sm" truncate w-full>{{ game.name }}</p>
      </div>
    </div>
  </div>
</template>


<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import SearchBox from '@/components/SearchBox/index.vue'
import { useGameList } from './composables/useGameList'

interface Game {
  id: string
  name: string
  image: string
}

const router = useRouter()
const { gameList, isLoading, hasError, fetchGameList } = useGameList()
const filteredGameList = ref<Game[]>([])

onMounted(async () => {
  await fetchGameList()
  filteredGameList.value = gameList.value
})

// 监听游戏列表变化，同步更新过滤后的列表
watch(gameList, (newGameList) => {
  if (filteredGameList.value.length === 0) {
    filteredGameList.value = newGameList
  }
})

const handleGameClick = (game: Game) => {
  console.log('点击了游戏:', game.name, game.id)
  // 跳转到统一的游戏绑定页面
  router.push({
    path: `/game/binding/${game.id}`,
    query: {
      gameName: game.name,
      logo: game.image
    }
  })
}
</script>

<style scoped>
.game-name {
  width: 100%;
  max-width: 88px;
}
</style>

<route lang="json">
{
	"meta": {
		"title": "游戏列表",
		"layout": "backHeader"
	}
}
</route>
