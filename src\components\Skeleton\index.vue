<script setup lang="ts">
import { computed } from 'vue'
import type { SkeletonProps } from './constants'

const props = withDefaults(defineProps<SkeletonProps>(), {
  type: 'card',
  count: 1,
  show: true,
  height: undefined,
  width: undefined,
  className: '',
  background: ''
})

// 计算骨架的样式
const styleObject = computed(() => {
  const style: Record<string, string> = {}
  if (props.height) style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  if (props.width) style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  return style
})

// 计算header背景样式
const headerBgStyle = computed(() => {
  if (props.background) {
    return { background: props.background }
  }
  return {}
})
</script>

<template>
  <template v-if="show">
    <!-- Header骨架屏 -->
    <div v-if="type === 'header'" class="skeleton-header animate-fadeIn" :class="className">
      <div 
        class="app-bar-skeleton" 
        :class="{ 'bg-gradient-to-b from-blue-gray-600 to-blue-gray-700': !background }"
        :style="[styleObject, headerBgStyle]"
      >
        <!-- 左侧图标区 -->
        <div class="flex items-center justify-between w-full">
          <div class="left-area flex-shrink-0 flex items-center">
            <div class="w-8 h-8 skeleton-loading-light rounded-full"></div>
          </div>
          
          <!-- 中间标题区 -->
          <div class="center-area flex-1 flex justify-center">
            <div class="w-32 h-6 skeleton-loading-light rounded"></div>
          </div>
          
          <!-- 右侧图标区 -->
          <div class="right-area flex-shrink-0 flex items-center">
            <div class="w-8 h-8 skeleton-loading-light rounded-full"></div>
          </div>
        </div>
        
        <!-- 用户信息区域 (展开时) -->
        <div class="user-info flex items-center mt-4">
          <div class="w-16 h-16 skeleton-loading-light rounded-full"></div>
          <div class="ml-3 flex flex-col gap-2">
            <div class="w-24 h-5 skeleton-loading-light rounded"></div>
            <div class="w-16 h-3 skeleton-loading-light rounded"></div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Banner骨架屏 -->
    <div v-else-if="type === 'banner'" class="skeleton-banner rounded-4 overflow-hidden animate-fadeIn" :class="className">
      <div class="w-full h-[176px] skeleton-loading" :style="styleObject"></div>
    </div>

    <!-- 功能卡片骨架屏 -->
    <div v-else-if="type === 'card'" class="flex justify-between flex-wrap animate-fadeIn" :class="className">
      <div 
        v-for="i in count" 
        :key="i" 
        class="service-card-skeleton w-[48%] h-[88px] rounded-xl mb-4 skeleton-loading"
        :style="styleObject"
      ></div>
    </div>

    <!-- 头像骨架屏 -->
    <div v-else-if="type === 'avatar'" class="flex items-end gap-3 animate-fadeIn" :class="className">
      <div class="flex flex-col w-18 relative">
        <div class="w-full aspect-square rounded-full skeleton-loading"></div>
        <div class="w-full h-6 rounded-full mt--8px skeleton-loading"></div>
      </div>
      <div class="flex flex-col gap-1 pb-1">
        <div class="h-6 w-24 skeleton-loading rounded"></div>
        <div class="h-4 w-16 skeleton-loading rounded"></div>
      </div>
    </div>

    <!-- 游戏列表骨架屏 -->
    <div v-else-if="type === 'gameList'" class="game-list animate-fadeIn" :class="className">
      <div 
        v-for="i in count" 
        :key="i"
        class="h-16 mb-3 rounded-[16px] skeleton-loading"
        :style="styleObject"
      ></div>
    </div>
    
    <!-- 网格布局骨架屏 -->
    <div v-else-if="type === 'grid'" class="grid grid-cols-3 gap-4 animate-fadeIn" :class="className">
      <div 
        v-for="i in count" 
        :key="i" 
        class="flex flex-col items-center"
      >
        <div class="w-28 h-28 rounded-lg skeleton-loading"></div>
        <div class="h-4 w-16 skeleton-loading rounded mt-2 mx-auto"></div>
      </div>
    </div>
  </template>
</template>

<style scoped>
.skeleton-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-loading-light {
  background: linear-gradient(90deg, rgba(255,255,255,0.15) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.15) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-header {
  overflow: hidden;
  width: 100%;
}

.app-bar-skeleton {
  display: flex;
  flex-direction: column;
  padding: 16px;
  height: 160px;
  width: 100%;
}

.app-bar-skeleton > .left-area,
.app-bar-skeleton > .center-area,
.app-bar-skeleton > .right-area {
  display: flex;
  align-items: center;
}

.app-bar-skeleton > .center-area {
  justify-content: center;
  flex: 1;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style> 