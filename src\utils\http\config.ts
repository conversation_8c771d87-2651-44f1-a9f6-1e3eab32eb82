/* httpNew 配置与类型定义 */
// 功能: 定义 http 客户端的核心配置与默认值，并提供合并工具

import type { AxiosRequestConfig } from 'axios'

/** 基础网络配置 */
export interface NetConfig extends Pick<AxiosRequestConfig, 'baseURL' | 'timeout' | 'withCredentials' | 'headers'> {}

/** 业务成功 / 失败码配置 */
export interface BusinessCodeConfig {
  /** 接口成功码，支持多个 */
  successCodes: number[]
  /** 需要触发刷新 Token 或重新登录的业务码 */
  tokenInvalidCodes: number[]
  /** 业务码中文信息映射（用于兜底） */
  codeMessage?: Record<number, string>
}

/** 重试配置 */
export interface RetryConfig {
  /** 默认最大重试次数 */
  maxRetries: number
  /** 重试延迟基数 (ms) */
  retryDelay: number
  /** 最大重试延迟 (ms) */
  maxRetryDelay: number
  /** 可重试的请求方法 */
  retryableMethods: string[]
}
export interface TokenConfig {
  /** Header 中携带 Token 的字段名 */
  headerKey: string
  /** Header 前缀，如 `Bearer ` */
  prefix: string
}

/** 错误提示文案 */
export interface ErrorMessageConfig {
  default: string
  network: string
  timeout: string
  /** 按 HTTP 状态码映射 */
  statusMessage: Record<number, string>
}

/** httpNew 全量配置 */
export interface HttpClientConfig {
  net: NetConfig
  business: BusinessCodeConfig
  token: TokenConfig
  retry: RetryConfig
  errorMessage: ErrorMessageConfig
}

export const defaultConfig: HttpClientConfig = {
	net: {
		baseURL: import.meta.env.VITE_APP_API_URL || '/',
		timeout: 15000,
		withCredentials: true
	},
	business: {
		successCodes: [0],
		tokenInvalidCodes: [401001, 401002, 500002],
		codeMessage: {
			0: '请求成功',
			1: '请求失败，请联系管理员',
			401001: 'Token无效',
			401002: 'Token已过期',
			404001: '用户不存在',
			500: '业务处理失败',
			500001: '认证信息缺失',
			500002: 'refresh_token无效',
			500003: '用户创建失败',
			5001: '请求已处理，请勿重复提交',
			5002: '请求正在处理',
			6002: '数据长度超过最大限制',
			12001: '非法的游戏ID',
			12002: '载荷数据过大',
			12003: '检测到潜在危险内容',
			12004: '请求数据必须为 JSON 对象',
			20001: '账号未找到，请检查后重试',
			429: '请求过于频繁，请稍后再试',
			/** 401 相关验签错误，统一描述 */
			401: '未授权或验签失败',
		},
	},
	token: {
		headerKey: 'accessToken',
		prefix: '',
	},
	retry: {
		maxRetries: 2,
		retryDelay: 1000,
		maxRetryDelay: 5000,
		retryableMethods: ['GET', 'HEAD', 'OPTIONS'],
	},
	errorMessage: {
		default: '未知错误',
		network: '网络连接异常，请检查网络设置',
		timeout: '请求超时，请稍后重试',
		statusMessage: {
			400: '请求参数错误',
			401: '未授权，请登录',
			403: '无权限访问',
			404: '接口不存在',
			500: '服务器内部错误',
			502: '网关错误',
			503: '服务不可用',
			504: '网关超时',
		},
	},
}

/**
 * 深度合并配置（仅对第一层做浅合并，深层对象递归）
 */
export function mergeConfig(custom: Partial<HttpClientConfig>): HttpClientConfig {
  const cfg = { ...defaultConfig, ...custom }
  cfg.net = { ...defaultConfig.net, ...(custom.net || {}) }
  cfg.business = {
    ...defaultConfig.business,
    ...(custom.business || {}),
    successCodes: custom.business?.successCodes || defaultConfig.business.successCodes,
    tokenInvalidCodes: custom.business?.tokenInvalidCodes || defaultConfig.business.tokenInvalidCodes,
    codeMessage: {
      ...(defaultConfig.business.codeMessage || {}),
      ...(custom.business?.codeMessage || {}),
    },
  }
  cfg.token = { ...defaultConfig.token, ...(custom.token || {}) }
  cfg.retry = { ...defaultConfig.retry, ...(custom.retry || {}) }
  cfg.errorMessage = {
    ...defaultConfig.errorMessage,
    ...(custom.errorMessage || {}),
    statusMessage: {
      ...defaultConfig.errorMessage.statusMessage,
      ...(custom.errorMessage?.statusMessage || {}),
    },
  }
  return cfg
} 