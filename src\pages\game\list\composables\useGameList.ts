import { ref } from 'vue'

interface Game {
  id: string
  name: string
  image: string
}

// 模拟游戏列表数据
const mockGameList: Game[] = [
  {
    id: '1',
    name: '王者荣耀',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '2',
    name: '和平精英',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '3',
    name: '英雄联盟',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '4',
    name: '原神',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '5',
    name: '明日方舟',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '6',
    name: '阴阳师',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '7',
    name: '崩坏：星穹铁道',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '8',
    name: '第五人格',
    image: 'https://via.placeholder.com/88'
  },
  {
    id: '9',
    name: '我的世界',
    image: 'https://via.placeholder.com/88'
  }
]

export function useGameList() {
  const gameList = ref<Game[]>([])
  const isLoading = ref(false)
  const hasError = ref(false)
  
  // 模拟获取游戏列表数据
  const fetchGameList = async () => {
    isLoading.value = true
    hasError.value = false
    
    try {
      // 模拟网络请求延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 设置模拟数据
      gameList.value = mockGameList
    } catch (error) {
      console.error('获取游戏列表失败:', error)
      hasError.value = true
    } finally {
      isLoading.value = false
    }
  }
  
  return {
    gameList,
    isLoading,
    hasError,
    fetchGameList
  }
} 