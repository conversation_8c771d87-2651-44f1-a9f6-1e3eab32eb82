<script setup lang="ts">
const props = defineProps<{
  ticket: {
    id: string
    title: string
    content: string
    status: 'processing' | 'completed'
    createTime: string
  }
}>()

const emit = defineEmits<{
  (e: 'click'): void
}>()

const handleClick = () => {
  emit('click', props.ticket)
}
</script>

<template>
  <div class="ticket-card bg-white rounded-4 p-4 mb-4" @click="handleClick">
    <div class="flex justify-between items-center">
      <span class="text-gray-500 text-md">服务单号：{{ ticket.id }}</span>
      <div 
        class="px-3 text-xs text-white rounded-full py-1"
        :class="ticket.status === 'processing' ? 'bg-yellow' : 'bg-green'"
      >
        {{ ticket.status === 'processing' ? '处理中' : '已完结' }}
      </div>
    </div>
    <div class="mt-3">
      <h3 class="text-lg font-medium h2">{{ ticket.title }}</h3>
      <p class="text-md text-gray-400 line-clamp-2 mt-3">{{ ticket.content }}</p>
    </div>
    <div class="mt-3 text-xs text-gray-400">{{ ticket.createTime }}</div>
  </div>
</template>

<style scoped>
.ticket-card {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}
</style> 