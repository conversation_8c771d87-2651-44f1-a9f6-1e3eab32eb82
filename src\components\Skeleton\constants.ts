export type SkeletonType = 
  | 'header'    // 头部区域骨架图
  | 'avatar'    // 用户头像区域
  | 'banner'    // 轮播图区域
  | 'card'      // 卡片区域
  | 'gameList'  // 游戏列表
  | 'grid';     // 网格布局

export interface SkeletonProps {
  // 骨架图类型
  type?: SkeletonType;
  // 数量 (适用于card, gameList, grid)
  count?: number;
  // 是否显示
  show?: boolean;
  // 高度 (可选，用于自定义高度)
  height?: string | number;
  // 宽度 (可选，用于自定义宽度)
  width?: string | number;
  // 自定义类名
  className?: string;
  // 自定义背景色 (主要用于header类型)
  background?: string;
} 