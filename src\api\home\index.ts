import { request } from '@/utils/http'
import * as types from './types'

/**
 * 首页模块API定义
 */
export const homeApi = {
  // 获取当前用户的绑定信息 - 真实API
  getUseruserGameList: () =>
    request<types.UserBindingInfo[]>({
      url: '/operation-wechat-service/u1/v1/home/<USER>',
      method: 'GET',
    }),

  // 游戏解绑 - 真实API
  unbindGame: (params: types.UnbindGameParams) =>
    request<string>({
      url: '/operation-wechat-service/u1/v1/home/<USER>',
      method: 'POST',
      data: params,
    }),

  // 切换激活游戏 - 真实API
  switchGame: (params: types.SwitchGameParams) =>
    request<null>({
      url: '/operation-wechat-service/u1/v1/home/<USER>',
      method: 'POST',
      data: params,
    })
}