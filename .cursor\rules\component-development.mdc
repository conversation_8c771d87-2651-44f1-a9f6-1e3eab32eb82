---
description: 组件开发规范，包括全局与页面组件、UI库使用和加载状态处理。
globs: 
alwaysApply: false
---
# 组件开发规范

本规则 **强制** 规定项目中 Vue 组件的开发、组织和使用规范。
**MUST**: 严格遵循以下规范。

## 组件分类与位置

*   **MUST**: 明确区分组件类型并放置在正确位置：
    *   **全局通用组件**: `src/components/` (可在项目任何地方复用，如 `AuthButton`, `DataList`)。
    *   **页面专属组件**: `src/pages/feature-name/components/` (仅在此页面或其子路由使用)。
    *   **布局组件**: `src/layouts/` (定义页面整体结构)。
*   **MUST**: 组件 **必须** 使用文件夹形式创建，入口文件为 `index.vue`。
    *   `src/components/UserProfileCard/index.vue`
    *   `src/pages/home/<USER>/GameCard/index.vue`
*   **MUST**: 组件文件名和在模板中使用时 **必须** 采用 `PascalCase` 命名。

## UI 库 (Varlet UI)

*   **MUST**: 项目统一使用 `Varlet UI` ([https://varlet.gitee.io/varlet-ui](mdc:https:/varlet.gitee.io/varlet-ui)) 作为基础 UI 组件库。
*   **SHOULD**: 优先使用 Varlet UI 提供的组件来构建界面，**AVOID** 造轮子。
*   **INFO**: Varlet UI 组件已通过 Vite 插件实现自动按需引入，无需在组件内手动 `import`。
*   **SHOULD**: 基于 Varlet UI 组件封装具有特定业务逻辑或符合项目设计规范的业务组件 (例如，带特定加载/错误状态的列表)。

## 组件接口 (Props, Emits, Slots)

*   **Props**:
    *   **MUST**: 使用 `<script setup>` 和 `defineProps<InterfaceName>()` 配合 `interface` 定义 Props 类型。
    *   **MUST**: 为所有 Props 提供明确的类型注解。
    *   **SHOULD**: 对非必需 Props 提供 `withDefaults` 默认值。
    *   **AVOID**: 在组件内部直接修改 Props 的值。
*   **Emits**:
    *   **MUST**: 使用 `defineEmits<{ (e: 'eventName', ...payload: any[]): void }>()` 定义组件触发的事件及其载荷类型。
    *   **MUST**: 对于 `v-model`，事件名 **必须** 为 `update:modelValue`。
    *   **SHOULD**: 事件命名采用 `kebab-case` 或 `camelCase` (与 HTML 事件监听器对齐，推荐 `kebab-case`，如 `item-click`)。
        ```vue
        <script setup lang="ts">
        interface Props { modelValue: string; label?: string }
        const props = withDefaults(defineProps<Props>(), { label: 'Default Label' });

        const emit = defineEmits<{ 
          (e: 'update:modelValue', value: string): void; 
          (e: 'submit', formData: Record<string, any>): void;
          (e: 'item-click', id: number): void;
         }>();

        function onInput(event: Event) {
          emit('update:modelValue', (event.target as HTMLInputElement).value);
        }
        function submitData() {
          const data = { /* ... */ };
          emit('submit', data);
        }
        </script>
        ```
*   **Slots**:
    *   **SHOULD**: 合理使用 Slots (`<slot>`, `<slot name="xxx">`) 提高组件的灵活性和复用性。
    *   **SHOULD**: 为 Slot 提供默认内容。
    *   **SHOULD**: 如果 Slot 内容复杂或需要传递数据，使用作用域插槽 (Scoped Slots)。
        ```vue
        <template>
          <header>
            <slot name="header">默认标题</slot>
          </header>
          <main>
            <slot :item="internalData">默认内容</slot>
          </main>
        </template>
        <script setup lang="ts">
        import { ref } from 'vue';
        const internalData = ref({ id: 1, name: 'Example' });
        </script>
        ```

## 状态处理 (Loading, Error, Empty)

*   **MUST**: 所有依赖异步数据的组件 **必须** 处理其加载（Loading）、错误（Error）和空（Empty）状态。
*   **Loading State**:
    *   **SHOULD**: 对于组件或内容区域加载，**优先** 使用骨架屏 (`<var-skeleton>`) 组件。
    *   **SHOULD**: 对于页面级加载或用户操作（如提交按钮），可以使用全局 Loading 指示器 (如 `Varlet LoadingBar`) 或按钮自身的 `loading` 状态。
*   **Error State**:
    *   **MUST**: 当数据加载失败时，**必须** 向用户显示清晰的错误提示。
    *   **SHOULD**: 提供重试操作的入口 (例如，一个“点击重试”按钮，触发 `refresh` 或 `run` 方法)。
*   **Empty State**:
    *   **MUST**: 当数据加载成功但列表或内容为空时，**必须** 显示空状态提示。
    *   **SHOULD**: 空状态提示应包含引导性信息或操作建议。

*   **示例 (结合 API 交互)**:
    ```vue
    <script setup lang="ts">
    // import { useMyData } from '~/composables/useMyData'
    // const { data, loading, error, refresh } = useMyData();
    const loading = ref(false); // 来自 useRequest
    const error = ref(null); // 来自 useRequest
    const data = ref([]); // 来自 useRequest
    const refresh = () => {}; // 来自 useRequest
    </script>

    <template>
      <MyComponentSkeleton v-if="loading" />
      <ErrorDisplay v-else-if="error" :error="error" @retry="refresh" />
      <DataList v-else-if="data && data.length > 0" :items="data" />
      <EmptyPlaceholder v-else message="暂无数据，请稍后再试" />
    </template>
    ```

</rewritten_file>