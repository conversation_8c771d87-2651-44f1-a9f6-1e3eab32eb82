import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useApiRequest } from '@/composables/useApiRequest'
import { rechargeApi } from '@/api'
import type { Game } from '@/api/recharge/types'

/**
 * 游戏列表功能Hook
 */
export function useRechargeGameList() {
  const userStore = useUserStore()
  const gameId = computed(() => String(userStore.activeGameId || ''))
  
  // 获取游戏列表数据
  const {
    data: gameListData,
    loading: isLoading,
    error: hasError,
    run: fetchGameListFromApi
  } = useApiRequest(
    () => rechargeApi.getRechargeGameListConfig(),
    {
      manual: true
    }
  )
  
  // 游戏列表数据
  const gameList = computed(() => gameListData.value?.data || [])
  
  // 获取游戏列表数据
  const fetchGameList = async () => {
    await fetchGameListFromApi()
  }
  
  onMounted(() => {
    fetchGameList()
  })
  
  return {
    gameList,
    isLoading,
    hasError,
    fetchGameList
  }
} 