<script setup lang="ts">
import { computed, ref } from 'vue'
import { useHomeData } from './composables/useHomeData'
import { useRouter } from 'vue-router'
import GameSwitchButton from './components/GameSwitchButton/index.vue'
import GameSwitchSidebar from './components/GameSwitchSidebar/index.vue'
import UserInfoCard from './components/UserInfoCard/index.vue'
import RoleInfoCard from './components/RoleInfoCard/index.vue'
import BannerCarousel from './components/BannerCarousel/index.vue'
import ServiceCard from './components/ServiceCard/index.vue'
import { useGameStore } from '@/stores/game'

// 使用首页数据
const {
  loading,
  error,
  currentGame,
  wechatUserInfo,
  currentTheme,
  banners,
  isEmptyState,
} = useHomeData()

const gameStore = useGameStore()
const router = useRouter()

// 侧边栏状态
const sidebarVisible = ref(false)

// 背景样式
const backgroundStyle = computed(() => {
  if (!currentTheme?.background) return {}
  return {
    backgroundImage: `url(${currentTheme.background})`,
    backgroundSize: 'cover',
    backgroundPosition: 'center'
  }
})

// 事件处理
const handleGameSwitch = () => {
  sidebarVisible.value = true
}

const handleRoleManage = () => {
  console.log('打开角色管理弹窗')
}

const handleRoleChanged = () => {
  // 角色变更后刷新首页数据
  refreshData()
  console.log('角色已变更，刷新首页数据')
}

const handleGameSwitched = () => {
  // 游戏切换后刷新首页数据
  refreshData()
  console.log('游戏已切换，刷新首页数据')
}

const handleServiceClick = (service: any) => {
  if (service.jumpUrl) {
    router.push(service.jumpUrl)
  } else {
    console.log('服务点击:', service)
  }
}

const handleBindGame = () => {
  router.push('/game/list')
}

const services = [
  {
    id: 1,
    title: '绑定游戏',
    image: 'https://via.placeholder.com/300x170/007AFF/FFFFFF?text=绑定游戏',
    jumpUrl: '/game/binding'
  },
  {
    id: 2,
    title: '帮助中心',
    image: 'https://via.placeholder.com/300x170/007AFF/FFFFFF?text=帮助中心',
    jumpUrl: '/help'
  }
]
</script>

<template>
  <div class="min-h-screen bg-[#F3F4F4] relative">
    <!-- 加载状态 -->
    <div v-if="loading" class="flex flex-col items-center justify-center min-h-screen gap-4">
      <div class="w-10 h-10 border-4 border-[#E5E5E5] border-t-[#007AFF] rounded-full animate-spin"></div>
      <p class="text-[#666] text-sm">加载中...</p>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="flex flex-col items-center justify-center min-h-screen gap-4">
      <p class="text-[#666] text-sm">{{ error }}</p>
      <button class="px-4 py-2 bg-[#007AFF] text-white border-none rounded-lg cursor-pointer text-sm" @click="refreshData">重试</button>
    </div>

    <!-- 主要内容 -->
    <div v-else class="relative min-h-screen p-4 pb-24 flex flex-col gap-4 home-content">
      <!-- 背景 -->
      <div 
        class="fixed top-0 left-0 w-full h-full -z-10 opacity-10"
        :style="backgroundStyle"
      ></div>

      <!-- 顶部区域 -->
      <div class="flex justify-between items-start gap-4 top-section">
        <GameSwitchButton 
          :game="currentGame"
          @click="handleGameSwitch"
        />
      </div>

      <!-- 用户信息 -->
      <div class="user-info-section">
        <UserInfoCard 
          :user-info="wechatUserInfo"
        />
      </div>

      <!-- 角色信息 -->
      <div class="mt-4" v-if="!isEmptyState">
        <RoleInfoCard 
          :game="currentGame"
          :binding="gameStore.activeRole"
          @manage="handleRoleManage"
          @role-changed="handleRoleChanged"
        />
      </div>

      <!-- 无状态提示 -->
      <div v-if="isEmptyState" class="flex items-center justify-center py-16">
        <div class="text-center">
          <p class="text-gray-500 mb-4">暂无绑定的游戏角色</p>
          <button 
            @click="handleBindGame"
            class="px-6 py-2 bg-[#007AFF] text-white rounded-lg text-sm"
          >
            绑定游戏
          </button>
        </div>
      </div>

      <!-- 广告轮播 -->
      <div class="mt-4" v-if="currentTheme?.banners?.length">
        <BannerCarousel 
          :banners="currentTheme.banners"
        />
      </div>

      <!-- 底部服务卡片 -->
      <div class="flex gap-3 mt-4 services-section">
        <ServiceCard 
          v-for="service in services"
          :key="service.id"
          :service="service"
          @click="handleServiceClick"
        />
      </div>

      <!-- 游戏切换侧边栏 -->
      <GameSwitchSidebar 
        v-model:visible="sidebarVisible"
        @game-switched="handleGameSwitched"
      />
    </div>
  </div>
</template>

<style scoped>
/* 响应式设计 */
@media (max-width: 375px) {
  .home-content {
    padding: 0.75rem;
    gap: 0.75rem;
  }
  
  .top-section {
    margin-top: 3rem;
  }
  
  .services-section {
    gap: 0.5rem;
  }
}
</style>

<route lang="json">
{
  "meta": {
    "title": "首页"
  }
}
</route>
