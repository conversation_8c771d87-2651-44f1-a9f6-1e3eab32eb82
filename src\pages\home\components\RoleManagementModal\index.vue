<template>
  <!-- 原生HTML弹窗实现 -->
  <Transition name="modal">
    <div 
      v-if="internalVisible"
      class="fixed inset-0 z-50 flex items-center justify-center"
    >
      <!-- 遮罩层 -->
      <div 
        class="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
        @click="handleClose"
      ></div>
      
      <!-- 弹窗内容 -->
      <div class="role-management-modal w-80% max-w-md relative z-10 bg-gray-100 rounded-xl p-4 flex flex-col items-center gap-3" @click.stop>
        <!-- 角色列表 -->
        <div class="w-full max-h-80 overflow-y-auto flex flex-col gap-3">
          <div 
            v-for="binding in userGameList" 
            :key="binding.bindingId"
            class="w-full bg-white rounded-lg p-3 flex justify-between items-center gap-3 shadow-sm cursor-pointer hover:bg-gray-50 transition-colors"
            @click="handleRoleClick(binding)"
          >
            <!-- 左侧角色信息 -->
            <div class="flex items-center gap-3 flex-1 pointer-events-none">
              <!-- 激活状态指示器 -->
              <div class="w-4 h-4 flex items-center justify-center flex-shrink-0">
                <div 
                  v-if="binding.isActive"
                  class="w-4 h-4 bg-lime-400 rounded-full flex items-center justify-center"
                >
                  <!-- 预留勾选图标位置 -->
                  <div class="w-3 h-3 text-black">
                    <div class="material-symbols:check" />
                  </div>
                </div>
                <div v-else class="w-4 h-4 bg-gray-300 rounded-full"></div>
              </div>

              <!-- 头像区域 -->
              <div class="relative w-10 h-10 flex-shrink-0">
                <img 
                  v-if="binding.avatar" 
                  :src="binding.avatar" 
                  :alt="binding.roleName"
                  class="w-8 h-8 rounded-lg object-cover absolute top-1 left-1"
                />
                <!-- 头像装饰框 -->
                <div class="absolute -top-0.5 -left-0.5 w-11 h-11 rounded-lg border border-white/20"></div>
              </div>

              <!-- 角色信息 -->
              <div class="flex-1 min-w-0">
                <div class="text-sm font-medium text-gray-900 text-center truncate">
                  {{ binding.roleName }}
                </div>
              </div>
            </div>

            <!-- 右侧操作按钮 -->
            <div class="flex-shrink-0 pointer-events-auto relative" @click.stop>
              <button 
                class="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded transition-colors"
                @click="toggleMenu(binding)"
              >
                <!-- 预留三个点图标位置 -->
                <div class="w-4 h-4 text-gray-600">
                  1
                  <div class="material-symbols:more-vert" />
                </div>
              </button>
              
              <!-- 菜单内容 -->
              <div 
                v-if="binding._showMenu"
                class="absolute right-0 top-8 bg-white rounded-lg shadow-lg py-2 min-w-30 z-20"
              >
                <button 
                  v-if="!binding.isActive"
                  @click="handleMenuAction('activate', binding)"
                  class="w-full px-4 py-3 text-left text-sm text-gray-900 hover:bg-gray-50 transition-colors"
                >
                  设为激活
                </button>
                <button 
                  @click="handleMenuAction('unbind', binding)"
                  class="w-full px-4 py-3 text-left text-sm text-red-500 hover:bg-gray-50 transition-colors"
                >
                  解绑
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部新增按钮 -->
        <div class="w-full flex justify-center">
          <button 
            @click="handleAddRole" 
            class="w-full h-12 bg-lime-400 hover:bg-lime-300 rounded-full flex items-center justify-center shadow-sm transition-colors"
          >
            <div class="relative w-8 h-8 flex items-center justify-center">
              <div class="absolute w-8 h-8 bg-lime-400 rounded-full"></div>
              <!-- 预留加号图标位置 -->
              <div class="relative z-1 w-5 h-5 text-black">
                1
                <div class="material-symbols:add-circle-outline-rounded" />
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useGameStore } from '@/stores/game'
import { useToast } from '@/composables/useToast'
import type { UserBinding } from '@/stores/game'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'role-changed'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部可见性状态
const internalVisible = ref(props.visible)

// 监听外部visible变化
watch(() => props.visible, (newValue) => {
  internalVisible.value = newValue
})

// 处理可见性变化
const handleVisibilityChange = (visible: boolean) => {
  internalVisible.value = visible
  emit('update:visible', visible)
}

const router = useRouter()
const gameStore = useGameStore()
const { showToast } = useToast()

// 当前游戏的所有绑定角色
const userGameList = computed(() => {
  const currentGameId = gameStore.activeGameId
  console.log(currentGameId)
  if (!currentGameId) return []
  return gameStore.getUserGameListByGameId(currentGameId).map(binding => ({
    ...binding,
    _showMenu: false // 添加菜单显示状态
  }))
})



// 切换菜单显示状态
const toggleMenu = (binding: UserBinding & { _showMenu?: boolean }) => {
  // 关闭其他菜单
  userGameList.value.forEach(b => {
    if (b.bindingId !== binding.bindingId) {
      b._showMenu = false
    }
  })
  // 切换当前菜单
  binding._showMenu = !binding._showMenu
}

// 处理角色点击
const handleRoleClick = async (binding: UserBinding & { _showMenu?: boolean }) => {
  if (!binding.isActive) {
    try {
      await gameStore.setActiveRole(binding)
      showToast('角色切换成功')
      emit('role-changed')
    } catch (error) {
      console.error('角色切换失败:', error)
      showToast('角色切换失败，请重试')
    }
  }
}

// 处理菜单操作
const handleMenuAction = async (action: string, binding: UserBinding & { _showMenu?: boolean }) => {
  try {
    if (action === 'activate') {
      await gameStore.setActiveRole(binding)
      showToast('角色切换成功')
      emit('role-changed')
    } else if (action === 'unbind') {
      // 确认解绑
      if (confirm(`确定要解绑角色 "${binding.roleName}" 吗？`)) {
        await gameStore.removeBinding(binding.bindingId)
        showToast('角色解绑成功')
        emit('role-changed')
      }
    }
  } catch (error) {
    console.error('操作失败:', error)
    showToast('操作失败，请重试')
  }
  
  // 关闭菜单
  binding._showMenu = false
}

// 处理新增角色
const handleAddRole = () => {
  const gameId = gameStore.activeRole?.gameId
  if (gameId) {
    router.push(`/game/binding/${gameId}`)
    handleClose()
  }
}

// 关闭弹窗
const handleClose = () => {
  internalVisible.value = false
  emit('update:visible', false)
}
</script>

<style scoped>

/* 模态框动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s ease;
}

.modal-enter-from {
  opacity: 0;
}

.modal-enter-to {
  opacity: 1;
}

.modal-leave-from {
  opacity: 1;
}

.modal-leave-to {
  opacity: 0;
}

/* 弹窗内容动画 */
.modal-enter-active .role-management-modal {
  animation: modalSlideIn 0.3s ease-out;
}

.modal-leave-active .role-management-modal {
  animation: modalSlideOut 0.3s ease-in;
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.9) translateY(20px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes modalSlideOut {
  from {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
  to {
    transform: scale(0.9) translateY(20px);
    opacity: 0;
  }
}

/* 滚动条样式 */
.role-list-container::-webkit-scrollbar {
  width: 4px;
}

.role-list-container::-webkit-scrollbar-track {
  background: #F0F0F0;
  border-radius: 2px;
}

.role-list-container::-webkit-scrollbar-thumb {
  background: #D0D0D0;
  border-radius: 2px;
}

.role-list-container::-webkit-scrollbar-thumb:hover {
  background: #B0B0B0;
}
</style>