import { ref, Ref, computed } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { rewardsApi } from '@/api'
import type { RewardConfig } from '@/api/rewards/types'

/**
 * 获取奖励配置
 * @param key 奖励页面的唯一标识
 * @returns 奖励配置信息
 */
export function useRewardConfig(key: Ref<string>) {
  // 获取奖励详情
  const {
    data: rewardDetailData,
    loading,
    error,
    run: fetchRewardDetail
  } = useApiRequest(
    () => rewardsApi.getRewardDetail(key.value),
    {
      manual: true,
      refreshDeps: [key]
    }
  )
  
  // 配置计算属性
  const config = computed<RewardConfig | null>(() => {
    if (!rewardDetailData?.value?.data) {
      return null
    }
    
    return rewardDetailData.value.data
  })
  
  // 刷新配置
  const refresh = () => {
    if (key.value) {
      fetchRewardDetail()
    }
  }

  return {
    config,
    loading,
    error,
    refresh
  }
} 