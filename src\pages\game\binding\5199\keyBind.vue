<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useClipboard } from '@vueuse/core'
import { useToast } from '@/composables/useToast'
import { request } from '@/utils/http'
import { Tabs, Tab } from '@varlet/ui'

const router = useRouter()
const toast = useToast()
const { copy, copied } = useClipboard()

// 状态管理
const activeTab = ref(0)
const bindKey = ref('')
const bindKeyLoading = ref(false)
const bindKeyError = ref('')

// 获取绑定密钥
const fetchBindKey = async () => {
  bindKeyLoading.value = true
  bindKeyError.value = ''
  
  try {
    const response = await request<{ code: number; message: string; data: { bindCode: string } }>({
      url: '/api/games/5199/bindKey',
      method: 'GET'
    })
    bindKey.value = response.data.data.bindCode || ''
  } catch (error: any) {
    console.error('获取绑定密钥失败:', error)
    bindKeyError.value = error.message || '获取密钥失败，请重试'
  } finally {
    bindKeyLoading.value = false
  }
}

// 复制绑定密钥
const copyBindKey = async () => {
  if (!bindKey.value) {
    toast.error('暂无可复制的密钥')
    return
  }
  
  await copy(bindKey.value)
  if (copied.value) {
    toast.success('密钥已复制到剪贴板')
  }
}

// 切换标签页
const handleTabChange = async (index: number) => {
  activeTab.value = index
  
  // 如果切换到官服&IOS标签页，获取绑定密钥
  if (index === 0 && !bindKey.value) {
    await fetchBindKey()
  }
}

// 跳转到其他渠道绑定页面
const goToOtherChannelBind = () => {
  router.push('/game/binding/5199')
}

// 下载游戏
const downloadGame = () => {
  window.open('https://www.5199.com/download', '_blank')
}

// 页面初始化
onMounted(async () => {
  // 默认获取绑定密钥
  if (activeTab.value === 0) {
    await fetchBindKey()
  }
})
</script>

<template>
  <div class="key-bind-container p-4 bg-gray-100 min-h-screen">
    <!-- 顶部标签页 -->
    <var-tabs
      v-model:active="activeTab"
      @change="handleTabChange"
      color="#F8F8F8"
      active-color="#FFD722"
      elevation="0"
    >
      <var-tab>官服&IOS</var-tab>
      <var-tab>其他渠道服</var-tab>
    </var-tabs>

    <!-- 内容区域 -->
    <div class="content bg-gray-200 rounded-xl p-4 mb-4 mt-4">
      <!-- 绑定账号标题 -->
      <h3 class="text-lg mb-3">绑定账号</h3>
      
      <!-- 官服&IOS内容 -->
      <template v-if="activeTab === 0">
        <div class="bg-black rounded-xl p-3 text-white mb-4">
          <p class="text-gray-400 text-sm mb-3">复制绑定口令，进入游戏完成绑定</p>
          <div class="flex items-center gap-3 cursor-pointer" @click="copyBindKey">
            <i class="i-carbon:copy text-xl text-yellow"></i>
            <div class="text-yellow text-xl font-bold truncate w-full whitespace-nowrap overflow-hidden text-ellipsis">
              <template v-if="bindKeyLoading">正在获取密钥...</template>
              <template v-else-if="bindKeyError">{{ bindKeyError }}</template>
              <template v-else>{{ bindKey || '点击获取密钥' }}</template>
            </div>
          </div>
        </div>
      </template>

      <!-- 其他渠道服内容 -->
      <template v-else>
        <div class="bg-black rounded-xl p-4 text-center mb-4">
          <p class="text-gray-400 text-sm mb-4">若您不是【官服】【TAPTAP】【好游快爆】【IOS平台】移动端的角色</p>
          <button 
            class="w-full bg-yellow text-black font-medium py-2 px-8 rounded-xl"
            @click="goToOtherChannelBind"
          >
            点击绑定
          </button>
        </div>
      </template>

      <!-- 获取游戏 -->
      <h3 class="text-lg mb-3">获取游戏</h3>
      <button 
        class="w-full border border-black rounded-xl py-2 mb-4 font-medium"
        @click="downloadGame"
      >
        立即下载
      </button>

      <!-- 绑定说明 - 仅在官服&IOS标签下显示 -->
      <template v-if="activeTab === 0">
        <h3 class="text-lg mb-3">绑定说明</h3>
        <div class="bg-white rounded-xl p-4 text-sm text-gray-500">
          <ul class="list-disc pl-5 space-y-3">
            <li>目前提供【官服】【TAPTAP】【好游快爆】【IOS平台】移动端的角色绑定操作:点击口令自动复制,手机打开《月圆之夜》最新版本游戏客户端,游戏将会自动验证你的口令信息。</li>
            <li>如进入游戏没有任何反应,请尝试:1)系统设置允许游戏读取剪切板;2)小退游戏重新进入</li>
            <li>如果无法绑定或绑定失败,请到游戏内找到"设置"-"客服帮助"尝试进行绑定。</li>
          </ul>
        </div>
      </template>
    </div>

    <!-- 底部客服链接 -->
    <div class="fixed bottom-4 left-0 right-0 flex items-center justify-center">
      <div class="text-sm text-[#B8B8B8]">
        遇到问题？点此
        <span class="text-[#FFD722] cursor-pointer" @click="router.push('/contact')">联系客服</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bg-yellow {
  background-color: #FFD722;
}

.text-yellow {
  color: #FFD722;
}

:deep(.var-tabs__tab) {
  flex: 1;
  border-radius: 9999px;
  transition: all 0.3s;
}

:deep(.var-tabs__tab--active) {
  font-weight: 500;
  color: #000 !important;
}

:deep(.var-tabs__indicator) {
  display: none;
}
</style>

<route lang="json">
{
  "meta": {
    "layout": "backHeader",
    "title": "月圆之夜密钥绑定"
  }
}
</route>
