<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { pinyin } from 'pinyin-pro'

interface Item {
  name: string
  [key: string]: any
}

interface Props {
  list: Item[]
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入游戏名称'
})

const emit = defineEmits<{
  'update:filtered-list': [filteredList: Item[]]
}>()

const keyword = ref('')
const filteredList = computed(() => {
  if (!keyword.value) return props.list
  
  return props.list.filter(item => {
    // 中文搜索
    if (item.name.includes(keyword.value)) {
      return true
    }
    
    // 拼音搜索
    const pinyinStr = pinyin(item.name, { toneType: 'none', type: 'array' }).join('')
    const pinyinFirstLetters = pinyin(item.name, { pattern: 'first', toneType: 'none', type: 'array' }).join('')
    
    return (
      pinyinStr.toLowerCase().includes(keyword.value.toLowerCase()) || 
      pinyinFirstLetters.toLowerCase().includes(keyword.value.toLowerCase())
    )
  })
})

watch(filteredList, (newList) => {
  emit('update:filtered-list', newList)
})

// 清空关键字
const clearKeyword = () => {
  keyword.value = ''
}
</script>

<template>
  <div class="search-box" bg="white" flex items-center p="x-4 y-3" rounded-4 gap-3>
    <div class="search-icon flex items-center justify-center">
      <var-icon name="magnify" color="#C7C7CC" size="16" />
    </div>
    <input
      v-model="keyword"
      :placeholder="placeholder"
      class="search-input flex-1"
      bg="transparent"
      border="none"
      outline="none"
      text="16px"
      font="medium"
      placeholder-color="#B8B8B8"
      font-family="PingFang SC, sans-serif"
    />
    <var-icon 
      v-if="keyword" 
      name="close" 
      color="#B8B8B8" 
      size="16" 
      @click="clearKeyword"
    />
  </div>
</template>

<style scoped>
.search-box {
  height: 42px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}
.search-input {
  width: 100%;
  line-height: 1.4;
}
</style> 