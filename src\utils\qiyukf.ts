import { systemApi } from '@/api'
import YSF from '@neysf/qiyu-web-sdk'

export class Qiyu {
  constructor() {
    this.init()
    this.config = {}
  }

  async init() {
    await YSF.init(import.meta.env.VITE_APP_QIYU_APP_KEY, {
      hidden: 1,
      subdomain: 1,
    }).then(() => {
      // eslint-disable-next-line no-console
      console.log('sdk加载成功---')
    }).catch((err) => {
      console.error('sdk加载失败---', err)
    })
  }

  async getRobotConfig(gameId: string) {
    return new Promise((resolve, reject) => {
      systemApi.getQiyuRobotConfig({ game_id: gameId }).then((res) => {
        this.config = {
          ...res.ysf_config,
          templateId: res.ysf_open.templateId,
        }
        resolve(this.config)
      }).catch((err) => {
        console.error('获取机器人配置失败---', err)
        // toast.error('联系客服失败，请刷新页面')
        reject(err)
      })
    })
  }

  async openQiyu(gameId: string) {
    if (!gameId) {
      // toast.error('好像出了一点问题...请重新尝试一下😭')
      return
    }
    // 如果config为空，重新获取
    if (Object.keys(this.config).length === 0) {
      await this.getRobotConfig(gameId)
    }

    await this.getRobotConfig(gameId).then((config) => {
      ysf('config', {
        ...config,
        layerSize: { width: '100%', height: '100vh' },
        success() {
          window.location.href = ysf('url', { templateId: config.templateId })
        },
      })
    }).catch(() => {
      // toast.error('联系客服失败，请刷新页面')
    })
  }
}
