<script setup lang="ts">
import { ref, watch } from 'vue'

interface Props {
  show: boolean
  rewardId: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:show', value: boolean): void
  (e: 'close'): void
}>()

// 内部显示状态
const isVisible = ref(false)

// 同步外部状态
watch(() => props.show, (val) => {
  isVisible.value = val
})

// 关闭弹窗
const handleClose = () => {
  isVisible.value = false
  emit('update:show', false)
  emit('close')
}
</script>

<template>
  <var-popup
    v-model:show="isVisible"
    position="center"
  >
    <div class="w-[300px] max-w-[90vw] bg-white rounded-xl p-5">
      <div class="flex flex-col items-center mb-4">
        <var-icon name="error_outline" color="#ff4757" size="32" />
        <h3 class="mt-3 mb-0 text-lg font-medium text-[#333]">未参与活动</h3>
      </div>
      
      <div class="mb-5">
        <p class="text-center text-sm text-[#666] m-0 leading-normal">
          您暂未参与该活动，无法领取奖励。请先参与活动后再来领取。
        </p>
      </div>
      
      <div class="flex justify-center">
        <var-button type="primary" block @click="handleClose">
          我知道了
        </var-button>
      </div>
    </div>
  </var-popup>
</template> 