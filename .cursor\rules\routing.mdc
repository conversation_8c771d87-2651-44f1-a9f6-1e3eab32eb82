---
description: 文件路由配置和路由参数的访问方式。
globs: 
alwaysApply: false
---
# 路由规范 (vite-plugin-pages)

本规则 **强制** 规定项目基于 `vite-plugin-pages` 的文件系统路由配置、路由信息访问、元信息定义和导航控制。
**MUST**: 严格遵循以下规范。

## 文件式路由 (`src/pages/`)

*   **MUST**: 所有页面级路由 **必须** 通过在 `src/pages` 目录下创建 `.vue` 文件或目录来定义。
*   **基本规则**:
    *   `src/pages/about.vue` -> `/about`
    *   `src/pages/user/profile.vue` -> `/user/profile`
    *   `src/pages/index.vue` -> `/` (根路由)
    *   `src/pages/game/index.vue` -> `/game`
*   **动态路由**:
    *   **MUST**: 使用方括号 `[]` 定义动态参数。
    *   `src/pages/game/[id].vue` -> `/game/:id`
    *   `src/pages/user/[userId]/settings.vue` -> `/user/:userId/settings`
    *   `src/pages/search/[...all].vue` -> `/search/*` (匹配所有后续段)
*   **嵌套路由**:
    *   **SHOULD**: 通过创建与 `.vue` 文件同名的目录，并在该目录下创建 `index.vue` 或其他子路由文件来实现。父级 `.vue` 文件 **必须** 包含 `<RouterView />`。
    ```plaintext
    pages/
    ├── user/
    │   ├── index.vue       # -> /user
    │   ├── [id].vue        # -> /user/:id
    │   └── settings.vue    # -> /user/settings
    └── posts.vue           # -> /posts (需要 <RouterView />)
        └── posts/
            ├── index.vue   # -> /posts
            └── [slug].vue  # -> /posts/:slug
    ```
*   **命名路由**: 路由名称由文件路径自动生成 (例如 `user-profile`)，通常无需手动指定。
*   **插件配置**: 参考 `vite.config.ts` 中的 `Pages({...})` 配置。

## 访问路由信息

*   **MUST**: 在组件的 `<script setup>` 中，**必须** 使用 `vue-router` 提供的 `useRoute()` 组合式函数来访问当前路由信息。
    ```vue
    <script setup lang="ts">
    import { useRoute } from 'vue-router'
    import { computed } from 'vue'

    const route = useRoute();

    // 访问路径参数 (params)
    const gameId = computed(() => route.params.id as string | undefined); // MUST: 注意类型
    const optionalParam = computed(() => route.params.optional as string | undefined);

    // 访问查询参数 (query)
    const searchTerm = computed(() => route.query.q as string | undefined);
    const page = computed(() => Number(route.query.page || '1')); // MUST: 处理类型转换

    // 访问路由名称和完整路径
    const routeName = computed(() => route.name);
    const fullPath = computed(() => route.fullPath);

    // 访问路由元信息 (meta)
    const requiresAuth = computed(() => route.meta.requiresAuth);
    const pageTitle = computed(() => route.meta.title);
    </script>
    ```
*   **MUST**: 访问 `route.params` 和 `route.query` 时，**必须** 注意其值可能是 `string`、`string[]` 或 `undefined`，并进行必要的类型断言、检查或转换。
*   **AVOID**: 尝试通过 `defineProps` 直接接收路由参数。**必须** 使用 `useRoute()`。

## 路由元信息 (Meta)

*   **SHOULD**: 使用 `<route>` 自定义块在 `.vue` 文件中定义路由元信息 (meta)。
    *   常用元信息：`title` (页面标题), `requiresAuth` (是否需要登录), `layout` (指定布局组件), `roles` (权限控制)。
    ```vue
    // src/pages/user/profile.vue
    <route lang="yaml">
    meta:
      title: '用户资料'
      requiresAuth: true
      layout: default # 对应 src/layouts/default.vue
      roles: ['user', 'admin']
    </route>

    <template>
      <div>用户资料页面</div>
    </template>
    ```
*   **INFO**: `vite-plugin-vue-layouts` 插件用于处理 `layout` 元信息。

## 编程式导航

*   **MUST**: 在 `<script setup>` 中，**必须** 使用 `vue-router` 提供的 `useRouter()` 组合式函数进行编程式导航。
    ```vue
    <script setup lang="ts">
    import { useRouter } from 'vue-router'

    const router = useRouter();

    function goToUserProfile(userId: string) {
      // 使用 path
      router.push(`/user/${userId}`);
      
      // 使用 name 和 params (推荐，更健壮)
      router.push({ name: 'user-id', params: { id: userId } });
    }

    function goBack() {
      router.go(-1);
    }

    function replaceWithLogin() {
      router.replace({ name: 'login', query: { redirect: route.fullPath } });
    }
    </script>
    ```
*   **SHOULD**: 优先使用命名路由 (`{ name: '...' }`) 进行导航，因为它对路径变化更健壮。
*   **MUST**: 区分 `router.push` (添加历史记录) 和 `router.replace` (替换当前记录)。

## 导航守卫 (Middleware)

*   **MUST**: 全局的导航守卫 (例如检查登录状态、权限) **必须** 定义在 `src/main.ts` 或单独的路由配置文件中 (如果未使用 `vite-plugin-pages` 的扩展功能)。
*   **SHOULD**: 对于页面级的特定守卫逻辑，考虑使用 `vue-router` 提供的组件内守卫 (`onBeforeRouteLeave`, `onBeforeRouteUpdate`) 或 `vite-plugin-pages` 可能提供的扩展功能（如路由元信息配合全局守卫）。
*   **INFO**: 本项目可能在 `src/middleware/` 目录下定义了可复用的路由中间件，并在全局守卫中根据 `route.meta` 应用。