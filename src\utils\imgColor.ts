class ThemeColor {
  imgUrl: string
  originalPixels: ImageData | null
  shrinkUrl: string
  themeColor: string
  themeColorCallBack: ((shrinkUrl: string, themeColor: string) => void) | null
  colorCountedSet: ColorCountedSet
  static colorCache: Map<string, string> = new Map() // 添加静态缓存

  constructor(imgUrl: string, callBack: (shrinkUrl: string, themeColor: string) => void) {
    this.imgUrl = imgUrl
    this.originalPixels = null
    this.shrinkUrl = ''
    this.themeColor = 'white'
    this.themeColorCallBack = callBack
    this.colorCountedSet = new ColorCountedSet()
    this.startScreeningThemeColor()
  }

  // 开始解析主色
  async startScreeningThemeColor() {
    // 检查缓存
    if (ThemeColor.colorCache.has(this.imgUrl)) {
      this.themeColor = ThemeColor.colorCache.get(this.imgUrl)!
      if (this.themeColorCallBack) {
        this.themeColorCallBack(this.shrinkUrl, this.themeColor)
      }
      return
    }

    try {
      await this.shrinkImage()
      this.screeningThemeColor()
      // 将结果存入缓存
      ThemeColor.colorCache.set(this.imgUrl, this.themeColor)
    }
    catch (error) {
      console.error('Error:', error)
    }
  }

  // 图片缩小
  async shrinkImage() {
    const image = new Image()
    image.crossOrigin = 'anonymous' // 添加这一行
    image.src = this.imgUrl
    await new Promise<void>((resolve) => {
      image.onload = () => resolve()
    })

    const width = image.width
    const height = image.height
    const shrinkFactor = 10
    const shrinkWidth = width / shrinkFactor
    const shrinkHeight = height / shrinkFactor

    const canvas = document.createElement('canvas')
    canvas.width = shrinkWidth
    canvas.height = shrinkHeight

    const ctx = canvas.getContext('2d')
    if (!ctx) {
      throw new Error('Failed to get canvas context')
    }

    ctx.drawImage(image, 0, 0, shrinkWidth, shrinkHeight)
    this.shrinkUrl = canvas.toDataURL('image/jpeg', 1)

    try {
      this.originalPixels = ctx.getImageData(0, 0, shrinkWidth, shrinkHeight)
    }
    catch (error) {
      console.error('Error:', error)
    }
  }

  // 开始筛选主题色
  screeningThemeColor() {
    if (!this.originalPixels || !this.originalPixels.data || this.originalPixels.data.length === 0) {
      throw new Error('像素为空')
    }

    for (let i = 0; i < this.originalPixels.data.length; i += 4) {
      const r = this.originalPixels.data[i]
      const g = this.originalPixels.data[i + 1]
      const b = this.originalPixels.data[i + 2]
      const a = this.originalPixels.data[i + 3] / 255.0

      // 添加一个色值范围，让它能忽略一定无效的像素值
      if (a > 0 && r < 200 && g < 200 && b < 200 && r > 50 && g > 50 && b > 50) {
        this.colorCountedSet.push(r, g, b, a)
      }
    }

    let maxCount = 0
    // 寻找出现次数最多的像素定为主色调
    this.colorCountedSet.forEach((value, key) => {
      if (maxCount <= value) {
        maxCount = value
        this.themeColor = `rgba(${key})`
      }
    })

    // 执行回调
    if (this.themeColorCallBack) {
      this.themeColorCallBack(this.shrinkUrl, this.themeColor)
    }
  }
}

// 统计不同像素的出现次数
class ColorCountedSet {
  private map: Map<string, number>

  constructor() {
    this.map = new Map<string, number>()
  }

  // 添加像素到集合
  push(r: number, g: number, b: number, a: number) {
    const identification = `${r},${g},${b},${a}`
    const count = this.map.get(identification) || 0
    this.map.set(identification, count + 1)
  }

  // 给 ColorCountedSet 操作类添加一个 forEach 方法
  forEach(cb: (value: number, key: string) => void) {
    this.map.forEach((value, key) => {
      cb(value, key)
    })
  }
}

export default ThemeColor
