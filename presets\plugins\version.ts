import type { Plugin } from 'vite'
import fs from 'node:fs'
import path from 'node:path'
import { execSync } from 'node:child_process'

interface VersionOptions {
  /**
   * 版本生成方式
   * @default 'timestamp'
   */
  type?: 'timestamp' | 'git' | 'custom'
  
  /**
   * 自定义版本号，仅在 type 为 'custom' 时生效
   */
  version?: string
  
  /**
   * 版本文件名称
   * @default 'version.json'
   */
  fileName?: string
}

/**
 * 获取 Git 提交哈希
 */
function getGitCommitHash(): string {
  try {
    return execSync('git rev-parse --short HEAD').toString().trim()
  }
  catch (e) {
    console.warn('无法获取 Git 提交哈希，将使用时间戳作为备选')
    return Date.now().toString()
  }
}

/**
 * 生成版本号
 */
function generateVersion(options: VersionOptions): string {
  const { type = 'timestamp' } = options
  
  if (type === 'custom' && options.version) {
    return options.version
  }
  
  if (type === 'git') {
    return getGitCommitHash()
  }
  
  return Date.now().toString()
}

/**
 * 版本生成插件
 * 用于在构建时生成包含版本信息的 JSON 文件，供前端检测更新
 */
export function createVersionPlugin(options: VersionOptions = {}): Plugin {
  const fileName = options.fileName || 'version.json'
  let distDir: string
  
  return {
    name: 'vite-plugin-version-generator',
    apply: 'build', // 仅在构建模式下应用
    
    configResolved(config) {
      distDir = config.build.outDir
    },
    
    closeBundle() {
      const version = generateVersion(options)
      const content = JSON.stringify({ version, generatedAt: new Date().toISOString() })
      
      const targetPath = path.resolve(distDir, fileName)
      fs.writeFileSync(targetPath, content)
      
      console.log(`✅ 版本文件已生成: ${targetPath} (version: ${version})`)
    }
  }
} 