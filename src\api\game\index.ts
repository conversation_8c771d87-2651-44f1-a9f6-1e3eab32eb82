import { request } from '@/utils/http'
import type { 
  GetuserGameListResponse, 
  UnbindGameParams, 
  UnbindGameResponse,
  SwitchActiveGameParams,
  SwitchActiveGameResponse
} from './types'

const DEFAULT_PREFIX = '/operation-bind-service/u1/v1/bindings'

// 游戏绑定相关接口
export const gameApi = {
  // 获取当前用户的绑定信息
  getBindList: () =>
    request<GetuserGameListResponse>({
      url: `${DEFAULT_PREFIX}/list`,
      method: 'GET',
    }),

  // 游戏解绑
  unbindGame: (params: UnbindGameParams) =>
    request<UnbindGameResponse>({
      url: `${DEFAULT_PREFIX}/unbind`,
      method: 'POST',
      data: params,
    }),

  // 切换活跃角色
  switchActiveGame: (params: SwitchActiveGameParams) =>
    request<SwitchActiveGameResponse>({
      url: '/operation-bind-service/u1/v1/user-active-game/switch-game',
      method: 'POST',
      data: params,
    })
}

export default gameApi
