<template>
  <div class="loading-state flex flex-col items-center justify-center py-10">
    <div class="loading-spinner mb-4"></div>
    <p class="text-gray-500">加载中...</p>
  </div>
</template>

<style scoped>
.loading-spinner {
  width: 36px;
  height: 36px;
  border: 3px solid #f5f5f5;
  border-top: 3px solid #ffd722;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 