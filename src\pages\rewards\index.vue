<template>
  <div class="rewards-page min-h-screen bg-gray-50 flex flex-col">
    <!-- 兑换码部分 -->
    <div class="p-4 bg-white shadow rounded-md mx-4 my-4">
      <h2 class="text-lg font-medium mb-3">奖励兑换</h2>
      <!-- 兑换码输入框 -->
      <div class="flex items-center gap-2">
        <var-input
          v-model="redeemCode"
          placeholder="请输入奖励兑换码"
          :disabled="redeemLoading"
          class="flex-1"
        />
        <var-button
          type="primary"
          :loading="redeemLoading"
          @click="redeemHandler"
        >
          兑换
        </var-button>
      </div>
    </div>
    
    <!-- 奖励列表 -->
    <div class="p-4 bg-white shadow rounded-md mx-4 mt-2 flex-1">
      <h2 class="text-lg font-medium mb-3">我的奖励</h2>
      <div class="mt-4">
        <!-- 待领取奖励列表 -->
        <div v-if="availableRewardsData?.data?.list?.length" class="grid grid-cols-2 gap-3">
          <div 
            v-for="reward in availableRewardsData.data.list" 
            :key="reward.id" 
            class="p-3 bg-blue-50 border border-blue-100 rounded-md shadow-sm"
            @click="() => router.push(`/rewards/${reward.id}`)"
          >
            <div class="flex items-center gap-2">
              <img 
                :src="reward.iconUrl" 
                alt="奖励图标" 
                class="w-10 h-10 object-contain"
              />
              <div>
                <div class="text-sm font-medium">{{ reward.title }}</div>
                <div class="text-xs text-gray-500 mt-1">
                  {{ reward.expireTime ? `有效期至: ${reward.expireTime}` : '永久有效' }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div 
          v-else 
          class="flex flex-col items-center justify-center py-8 text-gray-500"
        >
          <var-icon name="inbox" size="32" />
          <div class="mt-2 text-sm">暂无可领取奖励</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useRedeemCode } from './composables/useRedeemCode'
import { useApiRequest } from '@/composables/useApiRequest'
import { rewardsApi } from '@/api'

const router = useRouter()

// 使用兑换码功能
const {
  redeemCode,
  redeemLoading,
  redeemHandler
} = useRedeemCode()

// 获取可领取奖励列表
const {
  data: availableRewardsData,
  loading: availableRewardsLoading,
  run: fetchAvailableRewards
} = useApiRequest(
  () => rewardsApi.getAvailableRewards(),
  {
    manual: false
  }
)
</script>

<style scoped>

</style>

<route lang="json">
{
  "meta": {
    "title": "奖励中心",
    "layout": "backHeader"
  }
}
</route>
