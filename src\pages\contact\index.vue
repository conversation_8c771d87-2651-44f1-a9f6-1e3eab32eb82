<template>
  <div class="">
    <ListItem
        v-for="(item, index) in contactList"
        :key="index"
        :label="item.label"
        :left-icon="item.icon"
        :to="item.to"
        :onClick="item.onClick"
      />
  </div>
</template>

<script setup lang="ts">
import ListItem from '@/components/ListItem/index.vue'

const emit = defineEmits(['update-layout-menu'])
// 定义菜单数据
const menuItems = [
  {
    label: '更多服务',
    to: '/contact/more'
  }
]
emit('update-layout-menu', menuItems)

const contactList = [
  {
    label: '联系客服',
    icon: 'streamline-customer-support-1',
    onClick: () => window.open('tel:************')
  },
  {
    label: '服务进度',
    icon: 'mdi-history',
    to: '/contact/ticket'
  }
]


</script>

<style scoped>

</style>

<route lang="json">
{
  "meta": {
    "title": "联系我们",
    "layout": "backHeader"
  }
}
</route>
