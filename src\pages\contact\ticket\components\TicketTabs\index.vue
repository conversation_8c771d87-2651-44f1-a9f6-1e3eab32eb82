<script setup lang="ts">
import { ref, computed, watch } from 'vue'

const props = defineProps<{
  activeTab: string
}>()

const emit = defineEmits<{
  (e: 'change', tab: string): void
}>()

const tabs = [
  { id: 'all', label: '全部' },
  { id: 'processing', label: '处理中' },
  { id: 'completed', label: '已完结' }
]

// 使用本地状态进行管理
const currentTab = ref(props.activeTab)

// 监听props变化更新本地状态
watch(() => props.activeTab, (newVal) => {
  currentTab.value = newVal
})

// 处理tab切换
const handleTabChange = (tabId: string) => {
  currentTab.value = tabId
  emit('change', tabId)
}
</script>

<template>
  <div class="tabs-container flex border-b border-gray-100">
    <div 
      v-for="tab in tabs" 
      :key="tab.id"
      class="tab-item flex-1 text-center py-3 relative cursor-pointer"
      :class="currentTab === tab.id ? 'text-dark font-medium' : 'text-gray-500'"
      @click="handleTabChange(tab.id)"
      v-ripple
    >
      {{ tab.label }}
      <div 
        v-if="currentTab === tab.id" 
        class="tab-indicator absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-yellow-500"
      ></div>
    </div>
  </div>
</template>

<style scoped>
.tab-indicator {
  height: 2px;
  width: 50%;
  background-color: #ffc82c;
  border-radius: 1px;
}
</style> 