<script setup lang="ts">

const router = useRouter()

onMounted(() => {
	router.push('/home')
})

</script>

<template>
</template>

<style>

.code {
	margin-bottom: 20px;
}

.content {
	height: 40px;
}

.cover {
	height: auto;
	width: 700px;
	margin: 0 5px;
	max-width: 100%;
	max-height: 100%;
}

.desc {
	flex: 1;
	width: 300px;
}
</style>

<route lang="json">
{
	"meta": {
		"title": "404"
	}
}
</route>
