---
description: 组合式函数 (composables) 的编写与使用规范，包括 VueUse 集成。
globs: 
alwaysApply: false
---
# 组合式函数 (Composables) 规范

本规则 **强制** 规定项目中可复用组合式函数 (`composables`) 的创建、组织和使用规范。
**MUST**: 遵循以下规范封装和复用带有响应式状态的逻辑。

## 核心职责与定位

*   **MUST**: Composables 是封装**可复用、有状态逻辑**的核心机制。
    *   主要用于处理：**API 数据获取与管理 (配合 `@vue-request`)**、复杂组件内部逻辑、浏览器 API 交互 (事件监听、DOM 操作等)、共享的 UI 交互逻辑。
*   **MUST**: 明确区分 Composables 的存放位置：
    *   **全局复用**: `src/composables/`
    *   **页面/功能模块专属**: `src/pages/feature-name/composables/`
*   **AVOID**: 将纯粹的、无状态的工具函数放入 `composables` 目录，应放在 `src/utils/`。
*   **MUST**: **API 请求逻辑必须封装在 Composables 中**，而不是直接在 Vue 组件中调用 `useRequest`。参考 [API 交互规范 (api-interaction.mdc)](mdc:api-interaction.mdc)。

## 命名、输入与输出

*   **MUST**: Composable 函数名 **必须** 以 `use` 开头，采用 `camelCase`。 (`useUserProfile`, `usePaginatedList`, `useClickOutside`)
*   **Input (输入)**:
    *   **SHOULD**: 接受响应式引用 (`Ref<T>`) 或普通值作为参数，用于控制 Composable 的行为。
    *   **MUST**: 为所有输入参数提供明确的 TypeScript 类型。
*   **Output (输出)**:
    *   **MUST**: **必须** 返回一个对象，包含需要暴露给外部的响应式状态 (`ref`, `computed`) 和操作函数。
    *   **MUST**: 所有返回的状态 **必须** 是响应式的。
    *   **AVOID**: 返回一个巨大的、包含所有内部实现细节的对象。只暴露必要的接口。
    ```typescript
    // src/composables/useCounter.ts
    import { ref, computed, readonly } from 'vue'

    // 定义输入参数类型 (可选)
    interface UseCounterOptions {
      initialValue?: number;
      step?: number;
    }

    export function useCounter(options: UseCounterOptions = {}) {
      const { initialValue = 0, step = 1 } = options;

      // 内部状态
      const count = ref(initialValue);

      // 派生状态 (Getter)
      const isPositive = computed(() => count.value > 0);

      // 操作函数 (Action)
      function increment() {
        count.value += step;
      }

      function decrement() {
        count.value -= step;
      }

      // 返回公开接口
      return {
        // 只读状态 (如果外部不应直接修改)
        count: readonly(count),
        // 派生状态
        isPositive,
        // 操作函数
        increment,
        decrement,
      };
    }
    ```

## 副作用管理

*   **MUST**: 在 Composable 内部，**必须** 使用 Vue 的生命周期钩子 (`onMounted`, `onScopeDispose`, `onUnmounted`) 来正确管理任何需要清理的副作用。
    *   例如：定时器 (`setInterval`/`setTimeout`)、事件监听器 (`addEventListener`)、WebSocket 连接、第三方库实例等。
    ```typescript
    import { ref, onScopeDispose } from 'vue'

    export function useEventListener(target: EventTarget, event: string, callback: EventListener) {
      onMounted(() => target.addEventListener(event, callback));
      // onUnmounted/onScopeDispose 确保在组件卸载或 scope 销毁时移除监听器
      onScopeDispose(() => target.removeEventListener(event, callback));
    }
    ```
*   **INFO**: `onScopeDispose` 是更通用的清理钩子，在 `setup()` 作用域结束时触发（包括 `onUnmounted` 的情况）。

## 使用方式

*   **MUST**: 在需要使用 Composable 的组件或另一个 Composable 的 `<script setup>` 块顶部调用它。
*   **SHOULD**: 从返回的对象中解构出所需的状态和函数。
    ```vue
    <script setup lang="ts">
    import { useCounter } from '~/composables/useCounter'
    import { useEventListener } from '~/composables/useEventListener' // 假设已创建

    const { count, isPositive, increment } = useCounter({ initialValue: 10 });

    useEventListener(window, 'resize', () => {
      console.log('Window resized');
    });
    </script>
    ```

## VueUse 集成 (`@vueuse/core`)

*   **MUST**: **强烈推荐并优先** 使用 `VueUse` ([https://vueuse.org/](mdc:https:/vueuse.org)) 库提供的现成 Composables 来处理常见的交互模式和浏览器 API。
    *   示例：`useDebounceFn`, `useThrottleFn`, `useLocalStorage`, `useSessionStorage`, `useWindowScroll`, `useEventListener`, `onClickOutside`, `useClipboard`, `useFetch` (虽然本项目优先 `@vue-request`), `useRefHistory` 等。
*   **MUST**: 通过按需引入 (`import { xxx } from '@vueuse/core'`) 来使用 VueUse 函数，以优化打包体积。
*   **SHOULD**: 在封装自己的 Composable 之前，先检查 VueUse 是否已提供类似功能。
    ```typescript
    import { useLocalStorage, useDebounceFn, onClickOutside } from '@vueuse/core'
    import { ref } from 'vue'

    // 持久化用户设置
    const userSettings = useLocalStorage('user-settings', { theme: 'dark', notifications: true });

    // 防抖输入处理
    const handleInput = useDebounceFn((value: string) => {
      console.log('Debounced input:', value);
    }, 500);

    // 点击外部关闭
    const dropdownRef = ref(null);
    const isDropdownOpen = ref(false);
    onClickOutside(dropdownRef, () => {
      isDropdownOpen.value = false;
    });
    ```

## 示例

```typescript
// src/composables/useTimer.ts
import { ref, onUnmounted } from 'vue'

export function useTimer(intervalMs: number = 1000) {
  const seconds = ref(0)
  let timerId: number | null = null

  function start() {
    if (timerId === null) {
      timerId = setInterval(() => {
        seconds.value++
      }, intervalMs)
    }
  }

  function stop() {
    if (timerId !== null) {
      clearInterval(timerId)
      timerId = null
    }
  }

  onUnmounted(stop) // 组件卸载时自动停止计时器

  return { seconds, start, stop }
}
```