<template>
  <div class="flex items-center justify-center p-4 bg-white rounded-xl cursor-pointer transition-all duration-200 ease-in-out shadow-[0px_1px_4px_0px_rgba(0,0,0,0.15)] flex-1 hover:-translate-y-0.5 hover:shadow-[0px_4px_12px_0px_rgba(0,0,0,0.2)] active:translate-y-0" @click="handleClick">
    <div class="w-full h-full rounded-lg overflow-hidden flex items-center justify-center">
      <img 
        v-if="service.image" 
        :src="service.image" 
        :alt="service.title || '服务卡片'"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-16 h-16 text-[#666] flex items-center justify-center">
        <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
          <path d="M16 2L20.09 8.26L28 9.27L22 14.14L23.18 22.02L16 18.77L8.82 22.02L10 14.14L4 9.27L11.91 8.26L16 2Z" fill="currentColor"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ServiceItem {
  id: number
  title?: string
  image: string
  jumpUrl?: string
}

interface Props {
  service: ServiceItem
}

interface Emits {
  (e: 'click', service: ServiceItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('click', props.service)
}
</script>