import type { App } from 'vue'
import SentryPlugin from '@/utils/sentry'

export default (app: App) => {
  app.use(SentryPlugin, {
    dsn: import.meta.env.VITE_SENTRY_DSN || '',
    environment: import.meta.env.MODE,
    enableHttpMonitoring: true,
    httpMonitoringOptions: {
      // 忽略健康检查等接口
      ignoreUrls: [/\/ping$/, /\/health$/, /\/heartbeat$/],
      // 忽略常见的非关键错误状态码
      ignoreStatuses: [404, 429],
      // 生产环境采样率设置为0.5，仅上报50%错误
      sampleRate: import.meta.env.PROD ? 0.5 : 1.0,
      tags: {
        platform: 'wechat-web'
      }
    },
    // 性能追踪采样率
    tracesSampleRate: import.meta.env.PROD ? 0.1 : 1.0
  })
} 