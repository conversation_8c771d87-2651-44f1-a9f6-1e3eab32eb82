<script setup lang="ts">
const params = useRoute().params

const loading = ref(false)
const openQiyu = async () => {
  await new Promise(resolve => setTimeout(resolve, 1000))
}
onMounted(async () => {
  await openQiyu()
})
</script>

<template>
   <div class="flex items-center justify-center h-screen bg-primary">
    <var-loading class="m-auto" size="large" color="white" type="wave" />
   </div>
</template>

<style scoped>

</style>

<route lang="json">
{
  "meta": {
    "title": "联系客服",
    "layout": ""
  }
}
</route>
