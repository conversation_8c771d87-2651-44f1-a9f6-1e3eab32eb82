---
description: 项目背景概述、业务特性与技术定位。
globs: 
alwaysApply: false
---
# 项目背景与技术选型

本规则阐述项目核心业务、目标用户及关键技术选型，为开发提供宏观指导。

## 业务概述

*   **目标平台**: 微信服务号 H5 应用。
*   **核心用户**: 巨人网络游戏玩家。
*   **主要场景**: 在微信环境内提供便捷的游戏辅助服务，如账号管理、福利领取、充值等。

## 核心功能模块

*   **账号绑定**: 实现微信 openid 与游戏账号的关联。
*   **奖励中心**: 聚合各类游戏活动奖励和福利的领取入口。
*   **充值服务**: 提供安全便捷的游戏充值流程（集成微信支付）。
*   **客服支持**: 包括在线客服、工单提交与查询等。
*   **会员体系**: （待开发）提供会员专属权益和积分功能。

## 技术栈与架构原则

*   **核心框架**: `Vue 3` (Composition API) + `Vite` + `TypeScript`。
    *   **MUST**: 使用 Composition API 组织逻辑。
    *   **MUST**: 充分利用 TypeScript 的类型系统提高代码健壮性。
*   **UI 库**: `Varlet UI`。
    *   **SHOULD**: 优先使用 Varlet UI 提供的组件构建界面。
    *   **MUST**: 遵循 Varlet UI 的设计规范和最佳实践。
*   **状态管理**: `Pinia`。
    *   **MUST**: 使用 Pinia 进行跨组件、跨页面的状态共享。
*   **路由**: `vite-plugin-pages` (基于文件系统的路由)。
    *   **MUST**: 遵循文件路由的约定创建页面和配置路由元信息。
*   **CSS 方案**: `UnoCSS` (原子化 CSS)。
    *   **MUST**: 使用 UnoCSS 工具类进行样式开发，保持样式原子化。
*   **HTTP 请求**: `axios` 配合 `@vue-request`。
    *   **MUST**: 使用封装的 `http` 工具 (`src/utils/http`) 和 `@vue-request` 进行 API 请求。
*   **核心原则**: 轻量、快速、响应式、强类型、组件化、原子化 CSS。
*   **微信集成**: 深度利用 `微信 JS-SDK` 实现支付、分享、授权等功能。

## 关键业务特性

*   **认证流程**: 依赖微信授权获取用户信息，并与游戏账号体系打通。
*   **支付集成**: 主要依赖微信支付。
*   **数据驱动**: 关注用户行为数据收集与分析 (Clarity, Sentry)。

## 业务特性

▸ **活动对接**: 支持游戏内活动与微信端的数据互通，实现跨平台营销。
▸ **数据分析**: 追踪用户行为和转化路径，为运营决策提供数据支持。