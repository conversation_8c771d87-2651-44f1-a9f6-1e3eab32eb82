<template>
  <div class="">
    <ListItem
        v-for="(item, index) in moreList"
        :key="index"
        :label="item.label"
        :left-icon="item.icon"
        :to="item.to"
        :onClick="item.onClick"
      />
  </div>
</template>

<script setup lang="ts">
import ListItem from '@/components/ListItem/index.vue'


const moreList = [
  {
    label: '历史订单',
    onClick: () => console.log('历史订单')
  },
  {
    label: '开发票',
    onClick: () => console.log('开发票')
  },
  {
    label: '申请退款',
    onClick: () => console.log('申请退款')
  }
]


</script>

<style scoped>

</style>

<route lang="json">
{
  "meta": {
    "title": "更多服务",
    "layout": "backHeader"
  }
}
</route>
