import type { RouteLocationNormalized } from 'vue-router'
import defaultRules from '@/config/redirectRules'

// 重定向配置类型
export interface RedirectRule {
  // 原始路径（支持字符串或正则表达式）
  from: string | RegExp
  // 目标路径
  to: string
  // 是否保留查询参数
  preserveQuery?: boolean
  // 自定义参数转换函数
  transformQuery?: (query: Record<string, any>) => Record<string, any>
}

/**
 * 创建URL重定向中间件
 * @param customRules 自定义重定向规则，不提供则使用配置文件中的规则
 */
export const createRedirectMiddleware = (customRules?: RedirectRule[]) => {
  // 合并默认规则和自定义规则
  const rules = customRules || defaultRules

  /**
   * 如果需要重定向，返回目标对象；否则返回 null
   */
  return (to: RouteLocationNormalized) => {
    const currentPath = to.path

    // 查找匹配的重定向规则
    const matchedRule = rules.find((rule) => {
      if (typeof rule.from === 'string') return rule.from === currentPath
      if (rule.from instanceof RegExp) return rule.from.test(currentPath)
      return false
    })

    if (!matchedRule) return null

    let targetPath = matchedRule.to

    // 处理正则表达式捕获组
    if (matchedRule.from instanceof RegExp) {
      const matches = currentPath.match(matchedRule.from)
      if (matches) {
        matches.forEach((match, index) => {
          if (index > 0) targetPath = targetPath.replace(`$${index}`, match)
        })
      }
    }

    // 处理查询参数
    let query = { ...to.query }
    if (matchedRule.transformQuery) query = matchedRule.transformQuery(query)

    return {
      path: targetPath,
      query: matchedRule.preserveQuery ? query : {},
      hash: to.hash,
    }
  }
}

export default createRedirectMiddleware 