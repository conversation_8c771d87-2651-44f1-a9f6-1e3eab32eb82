// 配置查询请求参数
export interface ConfigQueryParams {
  configType: string
  configKey: string
  majorVersion?: number
  minorVersion?: number
}

// 配置查询响应，由于配置数据结构不定，这里使用 any
export type ConfigData = any

// 游戏基础配置项
export interface GameBasic {
  gameId: number
  name: string
  iconLogo: string // 短图
  wideLogo: string // 长图
  avatar: string
}


export interface ThemeBanner {
  id: number
  imageUrl: string
  jumpUrl?: string
  title?: string
}

export interface ThemeConfig {
  background: string
  logo: string
  banners: ThemeBanner[]
}

export interface HomeThemesConfig {
  emptyState: ThemeConfig
  gameThemes: Record<number, ThemeConfig>
}

// BaseResponse 与其他模块保持一致，直接复用
export interface BaseResponse<T> {
  code: number
  message: string
  data: T
}

// 配置查询响应
export interface ConfigQueryResponse extends BaseResponse<ConfigData> {} 