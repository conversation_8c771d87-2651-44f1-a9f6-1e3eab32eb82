/**
 * 游戏绑定配置
 * 专注于API路径配置和表单字段定义
 */

import { 
  GameBindingConfig, 
  BindingType, 
  FieldType 
} from './types'

// 球球大作战 (5078) - 短信验证绑定
export const game5078Config: GameBindingConfig = {
  gameId: '5078',
  gameName: '球球大作战',
  logoUrl: '/images/games/5078-logo.png',
  bindingType: BindingType.SMS,
  formFields: [
    {
      model: 'phoneNumber',
      label: '手机号',
      placeholder: '请输入手机号',
      type: FieldType.PHONE,
      required: true,
      validation: {
        pattern: '^1[3-9]\\d{9}$',
        message: '请输入正确的手机号码'
      }
    },
    {
      model: 'verificationCode',
      label: '验证码',
      placeholder: '请输入短信验证码',
      type: FieldType.VERIFICATION_CODE,
      required: true,
      hasVerificationButton: true,
      validation: {
        minLength: 4,
        maxLength: 6,
        pattern: '^\\d+$',
        message: '请输入正确的验证码'
      }
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5078/bind',
    sendCodeEndpoint: '/api/games/5078/sendCode'
  },
  buttonText: '绑定账号'
}

// 太空杀 (5256) - 短信验证绑定
export const game5256Config: GameBindingConfig = {
  gameId: '5256',
  gameName: '太空杀',
  logoUrl: '/images/games/5256-logo.png',
  bindingType: BindingType.SMS,
  formFields: [
    {
      model: 'phoneNumber',
      label: '手机号',
      placeholder: '请输入手机号',
      type: FieldType.PHONE,
      required: true,
      validation: {
        pattern: '^1[3-9]\\d{9}$',
        message: '请输入正确的手机号码'
      }
    },
    {
      model: 'verificationCode',
      label: '验证码',
      placeholder: '请输入短信验证码',
      type: FieldType.VERIFICATION_CODE,
      required: true,
      hasVerificationButton: true,
      validation: {
        minLength: 4,
        maxLength: 6,
        pattern: '^\\d+$',
        message: '请输入正确的验证码'
      }
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5256/bind',
    sendCodeEndpoint: '/api/games/5256/sendCode'
  },
  buttonText: '绑定账号'
}

// 街篮2 (5218) - API绑定
export const game5218Config: GameBindingConfig = {
  gameId: '5218',
  gameName: '街篮2',
  logoUrl: '/images/games/5218-logo.png',
  bindingType: BindingType.API,
  formFields: [
    {
      model: 'roleName',
      label: '角色昵称',
      placeholder: '请输入游戏中的角色昵称',
      type: FieldType.TEXT,
      required: true,
      validation: {
        minLength: 2,
        maxLength: 20,
        message: '角色昵称长度为2-20个字符'
      }
    },
    {
      model: 'gameId',
      label: '游戏ID',
      placeholder: '请输入游戏ID',
      type: FieldType.TEXT,
      required: true,
      validation: {
        pattern: '^\\d+$',
        message: '请输入正确的游戏ID'
      }
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5218/bind'
  },
  buttonText: '绑定账号'
}

// 五千年 (5286) - 带服务器选择的API绑定
export const game5286Config: GameBindingConfig = {
  gameId: '5286',
  gameName: '五千年',
  logoUrl: '/images/games/5286-logo.png',
  bindingType: BindingType.API,
  formFields: [
    {
      model: 'nickname',
      label: '游戏昵称',
      placeholder: '请输入游戏昵称',
      type: FieldType.TEXT,
      required: true,
      validation: {
        minLength: 2,
        maxLength: 20,
        message: '游戏昵称长度为2-20个字符'
      }
    },
    {
      model: 'gameId',
      label: '游戏ID',
      placeholder: '请输入游戏ID',
      type: FieldType.TEXT,
      required: true,
      validation: {
        pattern: '^\\d+$',
        message: '请输入正确的游戏ID'
      }
    },
    {
      model: 'server',
      label: '选择服务器',
      placeholder: '请选择服务器',
      type: FieldType.SELECT,
      required: true,
      // 动态获取服务器列表
      optionsApi: '/api/games/5286/servers'
    }
  ],
  apiConfig: {
    bindEndpoint: '/api/games/5286/bind'
  },
  buttonText: '绑定账号'
}

// 月圆之夜 (5199) - 其他渠道API绑定（官服密钥绑定单独处理）
export const game5199Config: GameBindingConfig = {
	gameId: '5199',
	gameName: '月圆之夜',
	logoUrl: '/images/games/5199-logo.png',
	bindingType: BindingType.API,
	formFields: [
		{
			model: 'roleName',
			label: '角色昵称',
			placeholder: '请输入游戏中的角色昵称',
			type: FieldType.TEXT,
			required: true,
			validation: {
				minLength: 2,
				maxLength: 20,
				message: '角色昵称长度为2-20个字符',
			},
		},
		{
			model: 'roleId',
			label: '游戏UID',
			placeholder: '请输入游戏中的UID',
			type: FieldType.TEXT,
			required: true,
			validation: {
				minLength: 2,
				maxLength: 12,
				pattern: '^[a-zA-Z0-9]+$',
				message: 'UID只能包含字母和数字，长度为8-12位',
			},
		},
	],
	apiConfig: {
		bindEndpoint: '/operation-yueyuanzhiye-service/bind/self-service',
	},
	buttonText: '绑定账号',
}

// 游戏配置映射表
export const gameBindingConfigs: Record<string, GameBindingConfig> = {
  '5078': game5078Config,
  '5256': game5256Config,
  '5218': game5218Config,
  '5286': game5286Config,
  '5199': game5199Config
}

/**
 * 根据游戏ID获取绑定配置
 */
export function getGameBindingConfig(gameId: string): GameBindingConfig | null {
  return gameBindingConfigs[gameId] || null
}

/**
 * 获取所有支持的游戏ID列表
 */
export function getSupportedGameIds(): string[] {
  return Object.keys(gameBindingConfigs)
}

/**
 * 检查游戏是否支持绑定
 */
export function isGameSupported(gameId: string): boolean {
  return gameId in gameBindingConfigs
}
