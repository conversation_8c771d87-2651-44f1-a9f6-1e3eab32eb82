import qs from 'qs'

// 授权作用域定义
const SCOPES: Array<string> = ['snsapi_base', 'snsapi_userinfo']

interface WechatAuthOptions {
  appid: string
  webid: string
  scope?: string
}

export default class WechatAuth {
  private appid: string
  private webid: string
  private scope: string
  private _code: string
  private _redirectUrl: string


  constructor(options: WechatAuthOptions) {
    this.appid = options.appid
    this.webid = options.webid
    this.scope = options.scope || SCOPES[1]
    this._code = ''
    this._redirectUrl = ''
  }

  static makeState(): string {
    return Math.random().toString(36).substring(2, 15)
      + Math.random().toString(36).substring(2, 15)
  }

  set redirectUri(redirectUri: string) {
    this._redirectUrl = encodeURIComponent(redirectUri)
  }

  get redirectUri() {
    return this._redirectUrl
  }

  set state(state: string) {
    localStorage.setItem('wechat_auth:state', state)
  }

  get state(): string {
    return localStorage.getItem('wechat_auth:state') || ''
  }

  get authUrl() {
    if (!this.appid)
      // eslint-disable-next-line no-throw-literal
      throw 'appid must not be empty'

    if (!this.redirectUri)
      // eslint-disable-next-line no-throw-literal
      throw 'redirect uri must not be empty'
    this.state = WechatAuth.makeState()

    return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${this.appid}&redirect_uri=${this.redirectUri}&response_type=code&scope=${this.scope}&state=${this.state}#wechat_redirect`
  }

  get code() {
    if (!this._code)
      // eslint-disable-next-line no-throw-literal
      throw 'Not get the code from wechat server!'
    const code = this._code
    this._code = ''
    return code
  }

  returnFromWechat(redirectUrl: string) {
    const parseUrl = qs.parse(redirectUrl.split('?')[1])
    if (import.meta.env.MODE === 'development') {
      this.state = ''
      this._code = String(parseUrl.code)
    }
    else {
      if (!this.state)
        throw new Error('You didn\'t set state')
      if (parseUrl.state === this.state) {
        this._code = String(parseUrl.code)
      }
      else {
        throw new Error(`Wrong state: ${parseUrl.state}`)
      }
      this.state = ''
    }
  }
}
