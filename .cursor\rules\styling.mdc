---
description: UnoCSS 原子化样式、响应式设计和布局指南。
globs: 
alwaysApply: false
---
# 样式与布局规范 (UnoCSS)

本规则 **强制** 规定项目中使用 `UnoCSS` 进行样式开发和响应式布局的规范。
**MUST**: 严格遵循原子化 CSS 的原则。
**INFO**: 参考配置文件 [uno.config.ts](mdc:../uno.config.ts)

## 原子化核心原则

*   **MUST**: **优先且主要** 使用 UnoCSS 提供的原子（工具）类来编写样式。
    ```vue
    <template>
      <button class="py-2 px-4 bg-blue-500 text-white font-semibold rounded shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75">
        原子化按钮
      </button>
    </template>
    ```
*   **MUST**: **AVOID** 编写传统的、非原子化的 CSS 类 (如 `.user-card`, `.product-title`)，除非在极少数必要情况下（见下文）。
*   **MUST**: 保持模板中类名的可读性和顺序（建议遵循 布局 → 间距 → 尺寸 → 字体/文本 → 颜色/背景 → 效果 → 交互状态 的大致顺序）。
*   **SHOULD**: 利用 UnoCSS 的 **属性化模式 (Attributify Mode)** 进一步简化模板 (如果团队接受)。
    ```vue
    <button 
      py-2 px-4 
      bg="blue-500 hover:blue-700" 
      text="white sm" 
      font="semibold" 
      rounded shadow-md 
      focus="outline-none ring-2 ring-blue-400 ring-opacity-75">
      属性化按钮
    </button>
    ```

## 常用工具类示例

*   **布局 (Layout)**:
    *   Flexbox: `flex`, `inline-flex`, `items-center`, `items-start`, `justify-center`, `justify-between`, `flex-col`, `flex-row`, `flex-1`, `flex-grow`, `flex-shrink-0`
    *   Grid: `grid`, `inline-grid`, `grid-cols-3`, `grid-rows-2`, `gap-4`, `col-span-2`, `row-start-1`
    *   Positioning: `relative`, `absolute`, `fixed`, `top-0`, `left-1/2`, `-translate-x-1/2` (居中), `z-10`
    *   Display: `block`, `inline-block`, `hidden` (`display: none`)
*   **间距 (Spacing)**:
    *   Padding: `p-4` (all), `px-2` (x-axis), `pt-8` (top)
    *   Margin: `m-auto` (center), `mx-4` (x-axis), `mb-6` (bottom)
    *   Space Between: `space-x-4` (horizontal children), `space-y-2` (vertical children)
*   **尺寸 (Sizing)**:
    *   Width/Height: `w-full`, `h-screen`, `w-1/2`, `h-32`, `min-w-xs`, `max-h-lg`
*   **文本与字体 (Typography)**:
    *   Font Size: `text-sm`, `text-lg`, `text-xl`
    *   Font Weight: `font-normal`, `font-medium`, `font-semibold`, `font-bold`
    *   Text Color: `text-gray-900`, `text-red-500`, `dark:text-white` (暗黑模式)
    *   Text Alignment: `text-center`, `text-left`, `text-right`
    *   Line Clamp: `line-clamp-2` (文本截断)
*   **背景与边框 (Backgrounds & Borders)**:
    *   Background Color: `bg-white`, `bg-gray-100`, `dark:bg-dark-800`
    *   Border Radius: `rounded`, `rounded-full`, `rounded-t-lg`
    *   Border Width/Color: `border`, `border-2`, `border-gray-300`, `border-dashed`
*   **效果 (Effects)**:
    *   Shadow: `shadow-sm`, `shadow-md`, `shadow-lg`
    *   Opacity: `opacity-75`
*   **交互状态 (Interactivity)**:
    *   Hover/Focus/Active: `hover:bg-gray-200`, `focus:border-blue-500`, `active:scale-95`
    *   Disabled: `disabled:opacity-50`, `disabled:cursor-not-allowed`

## 响应式设计

*   **SHOULD**: **优先** 使用 **容器查询 (`@container`)** 实现组件内部的响应式布局，使组件更具自适应性。
    *   在父元素上添加 `@container` 类。
    *   使用 `@<breakpoint>:` 前缀，如 `@sm:p-8`, `@md:flex`。
    ```vue
    <div class="@container bg-gray-100 p-4 rounded">
      <h3 class="text-lg font-semibold @md:text-xl">根据容器大小变化</h3>
      <p class="text-sm @lg:text-base">在不同容器尺寸下，文本大小会调整。</p>
    </div>
    ```
*   **SHOULD**: 仅在需要根据**整个屏幕视口宽度**进行布局调整时，才使用传统的屏幕断点前缀 (`sm:`, `md:`, `lg:`, `xl:`, `2xl:`)
    *   例如：`md:grid-cols-2`, `lg:hidden`
*   **INFO**: 断点定义在 `uno.config.ts` 的 `theme.breakpoints` 中。

## 特殊情况与 `@apply`

*   **MUST**: **严格限制** `@apply` 的使用。
*   **WHEN TO USE `@apply` (谨慎)**:
    *   处理无法直接应用工具类的第三方组件样式覆盖。
    *   定义极少数、在多处重复使用的、非常复杂的样式组合，且封装成组件不划算时 (需要团队评估)。
    *   定义全局基础样式 (如 `body`, `a` 标签) 时，可在 `src/styles/main.css` 中使用。
*   **HOW TO USE `@apply`**:
    *   **MUST**: 在 Vue 组件的 `<style scoped>` 或全局 CSS 文件中使用。
    *   **AVOID**: 过度使用 `@apply`，它会降低原子化的优势和可维护性。
    ```vue
    <style scoped>
    /* 谨慎使用示例: 定义一个特定按钮的基础样式 */
    .complex-reusable-button {
      @apply px-3 py-1.5 rounded text-sm font-medium transition-colors duration-150;
      @apply bg-purple-600 text-white hover:bg-purple-700;
      @apply focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2;
    }
    </style>
    ```

## 维护与重构

*   **MUST**: 当发现样式组合变得过于复杂或在多处重复时，**优先考虑将其重构为一个独立的 Vue 组件**，而不是滥用 `@apply`。
