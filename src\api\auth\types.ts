import type { ApiResponse as HttpApiResponse } from '@/utils/http'

// 登录请求参数
export interface LoginParams {
  code: string
  appId: string
  platformType: 'WEAMP' | 'WECHAT'
}

// 用户信息
export interface UserInfo {
  id: number
  nickname: string
  sex: number
  province: string
  city: string
  country: string
  headimgurl: string
  privileges: string[]
  unionid: string
  createTime: string
  updateTime: string
}

// 权限凭证信息
export interface TokenInfo {
  accessToken: string
  refreshToken: string
  accessTokenExpiresIn: number
  refreshTokenExpiresIn: number
  user: UserInfo
}

// 刷新token请求参数
export interface RefreshTokenParams {
  refreshToken: string
}

// 删除旧 ApiResponse 定义并重定向到 http 模块
export type ApiResponse<T = any> = HttpApiResponse<T> 