<script setup lang="ts">
import Navigation from './components/Navigation.vue'

</script>

<template>
	<div class="bg-white-1 h-screen flex flex-col">
		<!-- 主要内容区域 -->
		<main class="flex-1 overflow-hidden">
			<router-view v-slot="{ Component }">
				<transition name="fade" mode="out-in">
					<component :is="Component" />
				</transition>
			</router-view>
		</main>

		<!-- 底部导航栏 -->
		<Navigation
			class="mx-4 fixed bottom-4 left-0 right-0 z-40 bg-white"
			style="border-radius:3.5rem;box-shadow:0 1px 4px 0 rgba(0,0,0,0.25);"
		/>
	</div>
</template>

<style>
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.25s ease;
}

.fade-enter-from,
.fade-leave-to {
	opacity: 0;
}
</style>
