<script setup lang="ts">
import { useRoute } from 'vue-router'

const route = useRoute()

const navItems = [
	{
		label: '充值',
		path: '/recharge',
		icon: new URL('@/assets/images/fwh/home/<USER>', import.meta.url).href,
		iconActive: new URL('@/assets/images/fwh/home/<USER>', import.meta.url).href


	},
	// {
	// 	label: '会员',
	// 	path: '/vip',
	// 	icon: '👑'
	// },
	{
		label: '我的',
		path: '/home',
		icon: new URL('@/assets/images/fwh/home/<USER>', import.meta.url).href,
		iconActive: new URL('@/assets/images/fwh/home/<USER>', import.meta.url).href


	}
]
</script>

<template>
	<nav class="h-18 flex items-center justify-around px-4">
		<router-link
			v-for="item in navItems"
			:key="item.path"
			:to="item.path"
			class="flex flex-col items-center text-gray-500"
		>
			<div class="icon-container flex items-center justify-center">
				<img 
					:src="route.path === item.path ? item.iconActive : item.icon" 
					alt="icon" 
					:class="['nav-icon', route.path === item.path ? 'nav-icon-active' : '']"
				/>
			</div>
			<span :class="['text-3', 'mt-1', route.path === item.path ? 'text-dark' : 'text-dark-2']">{{ item.label }}</span>
		</router-link>


	</nav>
</template>

<style scoped>
.icon-container {
	width: 40px;
	height: 40px;
	position: relative;
}

.nav-icon {
	width: 32px;
	height: 32px;
	transition: all 0.2s ease; 
}

.nav-icon-active {
	transform: scale(1.25);
}


</style>
