import { ref, computed } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { rewardsApi } from '@/api'
import { useUserStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'
import type { RedeemCodeParams } from '@/api/rewards/types'

/**
 * 奖励兑换码功能Hook
 */
export function useRedeemCode() {
  const userStore = useUserStore()
  const toast = useToast()
  const gameId = computed(() => userStore.activeGameId)
  
  // 兑换码输入
  const redeemCode = ref('')
  
  // 兑换码API
  const {
    data: redeemData,
    loading: redeemLoading,
    run: redeemCodeApi
  } = useApiRequest(
    (code: string) => rewardsApi.redeemCode({
      code,
      gameId: gameId.value
    }),
    {
      manual: true,
      onSuccess: (data) => {
        if (data.data.success) {
          toast.success('兑换成功')
          // 清空输入
          redeemCode.value = ''
        } else {
          toast.error(data.data.message)
        }
      },
      onError: () => {
        toast.error('兑换失败，请稍后重试')
      }
    }
  )
  
  // 兑换函数
  const redeemHandler = () => {
    if (!redeemCode.value) {
      toast.warning('请输入兑换码')
      return
    }
    
    redeemCodeApi(redeemCode.value)
  }
  
  return {
    redeemCode,
    redeemLoading,
    redeemData,
    redeemHandler
  }
} 