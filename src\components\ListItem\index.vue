<template>
	<div
		class="flex items-center justify-between p-4 bg-white text-dark"
		@click="handleClick"
	>
		<div class="flex items-center ">
			<div 
				v-if="leftIcon" 
				:class="[`i-${leftIcon}`, ' mr-3 text-lg w-6 h-6 ']"
			></div>
			<span class="text-base ">{{ label }}</span>
		</div>
		<div class="flex items-center">
			<div 
					:class="[`i-${rightIcon || 'mdi-chevron-right'}`, 'text-lg w-6 h-6']"
				></div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

interface Props {
	label: string
	leftIcon?: string
	rightIcon?: string
	to?: string
	onClick?: () => void
}

const props = withDefaults(defineProps<Props>(), {
	leftIcon: '',
	rightIcon: '',
})

const router = useRouter()

const handleClick = () => {
	if (props.onClick) {
		props.onClick()
	} else if (props.to) {
		router.push(props.to)
	}
}
</script>
