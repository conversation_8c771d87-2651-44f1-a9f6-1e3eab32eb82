---
description:
globs:
alwaysApply: false
---
# 项目功能介绍

## 总体概述

本项目是一个基于 Vue 3 和 TypeScript 的微信公众号 Web 应用，主要提供游戏服务相关功能，包括游戏账号绑定、奖励领取、充值和客服支持等核心业务。应用采用模块化设计，各功能模块相对独立，同时保持数据共享和状态一致性。

## 核心功能模块

### 首页 (Home)

首页是用户的主要入口，提供以下功能：

- **游戏账号管理**：展示用户已绑定的游戏账号列表，并允许用户切换当前选中的游戏账号
- **主题适配**：根据当前选中的游戏，动态调整页面主题（如背景、色调等）
- **功能导航**：提供客服、领奖、充值等核心功能的快捷入口
- **游戏绑定**：提供入口允许用户绑定新的游戏账号

**关键交互流程**：
1. 用户进入首页时，系统自动加载用户绑定的游戏账号列表和游戏配置
2. 用户选择不同游戏账号后，系统会：
   - 更新当前激活的账号状态
   - 根据选中游戏更新页面主题
   - 发送 API 请求通知后端用户当前选择的角色
3. 所有后续操作（客服、领奖等）都基于当前选中的游戏账号进行处理

### 游戏绑定 (Game)

游戏绑定模块允许用户关联他们的游戏账号：

- **游戏列表**：展示可供绑定的游戏列表
- **账号绑定**：提供表单让用户输入游戏 UID 和绑定码进行验证
- **绑定结果**：显示绑定成功或失败的结果，并在成功时自动将新绑定账号设为当前选中账号

**关键交互流程**：
1. 用户从首页进入绑定页面并选择游戏
2. 系统可能提供不同游戏专属的绑定流程（基于不同的 gameId）
3. 用户提交绑定信息后，系统验证并完成绑定
4. 绑定成功后，新绑定的账号会自动设为当前激活账号

### 奖励领取 (Rewards)

奖励领取模块让用户可以获取游戏内奖励：

- **奖励列表**：展示可领取的奖励包列表
- **奖励详情**：显示单个奖励包的详细内容
- **领取功能**：允许用户领取奖励或使用兑换码
- **历史记录**：查看已领取的奖励记录

**关键交互流程**：
1. 系统根据当前选中的游戏账号加载对应的可用奖励
2. 用户可以直接领取奖励或输入兑换码获取奖励
3. 领取成功后，系统更新奖励状态并可能显示领取成功的提示

### 客服支持 (Contact)

客服模块提供用户与客服团队沟通的渠道：

- **工单系统**：允许用户创建、查看和跟进客服工单
- **工单详情**：显示工单的详细内容和状态
- **催促功能**：允许用户催促客服处理工单

**关键交互流程**：
1. 用户基于当前选中的游戏账号创建工单
2. 系统记录工单信息并关联到特定游戏账号
3. 用户可以查看工单处理状态或催促客服响应

### 充值功能 (Recharge)

充值模块提供游戏内货币或服务的付费功能：

- **充值选项**：展示可用的充值渠道和金额
- **VIP 优化**：根据用户 VIP 等级调整界面显示和可用选项
- **交易处理**：引导用户完成支付流程

**关键交互流程**：
1. 系统根据当前选中的游戏账号和用户 VIP 等级加载充值配置
2. 用户选择充值金额和支付方式
3. 系统生成支付订单并引导用户完成支付

## 核心数据流

整个应用的核心数据流围绕"当前选中的游戏账号"展开：

1. 首页加载用户的游戏账号列表和当前选中账号
2. 用户切换选中账号时，系统更新全局状态并通知后端
3. 其他模块（客服、领奖、充值）基于当前选中账号加载相应数据和功能
4. 新绑定游戏账号后，该账号自动成为当前选中账号

## 技术特点

- **响应式设计**：使用 Vue 3 的组合式 API 和响应式系统
- **状态管理**：通过 Pinia 实现全局状态管理，特别是用户和游戏账号信息
- **主题定制**：支持基于不同游戏的动态主题切换
- **API 交互**：统一的 API 请求处理和错误管理机制
- **组件复用**：高度模块化的设计促进组件和逻辑的复用
