export interface GameConfig {
  id: string // 系统内部使用的英文标识
  name: string // 显示用的中文名称
  shortName?: string // 可选的简称
  gameID: string
  tag?: string // 添加tag字段用于显示标识
}

export const GAME_CONFIGS: GameConfig[] = [
  { id: 'yyzy', name: '月圆之夜', gameID: '5199', tag: 'UID' },
  { id: 'qiuqiu', name: '球球大作战', gameID: '5078', tag: '球球号' },
  { id: 'taikongsha', name: '太空杀', gameID: '5256', tag: '太空号' },
  { id: 'aierzhiguang', name: '艾尔之光', gameID: '25' },
  { id: 'xianxia', name: '仙侠世界', gameID: '35' },
  { id: 'xianxia2', name: '仙侠世界2', gameID: '45' },
  { id: 'jielan2', name: '街篮2', gameID: '5218', tag: 'ID' },
  { id: 'slg5k', name: '五千年', gameID: '5286' },
  { id: 'zhengtu', name: '征途', gameID: '' },
]

// 创建中文名到ID的映射
export const GAME_NAME_TO_ID = Object.fromEntries(
  GAME_CONFIGS.map(game => [game.name, game.id]),
)

// 创建ID到中文名的映射
export const GAME_ID_TO_NAME = Object.fromEntries(
  GAME_CONFIGS.map(game => [game.id, game.name]),
)

// 创建gameID到tag的映射
export const GAME_ID_TO_TAG = Object.fromEntries(
  GAME_CONFIGS.filter(game => game.tag)
    .map(game => [game.gameID, game.tag]),
)

export const ASSETS_BASE_URL = 'https://cs-unit-open-1251265365.cos.ap-shanghai.myqcloud.com/public/images'
