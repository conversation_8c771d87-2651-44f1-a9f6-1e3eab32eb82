---
description: 项目规则索引文件，包含所有模块化规则的概述。
globs: ["**/*"]
alwaysApply: true
---
# 项目开发规则索引

本文件是项目所有 Cursor Rules 的中心索引。使用本文档可以快速导航至特定的开发规范和最佳实践。

**请在执行相关任务前，优先查阅对应的规则文件。**

## 核心与基础

*   **[项目背景 (project-background.mdc)](mdc:project-background.mdc)**: 理解项目目标、主要功能和技术选型。
*   **[项目功能 (project-features.mdc)](mdc:project-features.mdc)**: 了解项目的核心功能模块、交互流程和数据流。
*   **[核心概念 (core-concepts.mdc)](mdc:core-concepts.mdc)**: 掌握 Vue 3、Vite、TypeScript 的基础用法和本项目约定。
*   **[项目结构 (project-structure.mdc)](mdc:project-structure.mdc)**: 遵循标准的目录组织、文件和代码元素命名规范。

## 开发实践

*   **[API 交互 (api-interaction.mdc)](mdc:api-interaction.mdc)**: 规范 API 请求的发起 (`@vue-request`)、响应处理和相关状态管理。
*   **[组件开发 (component-development.mdc)](mdc:component-development.mdc)**: 组件（全局/页面级）设计、Varlet UI 使用、加载和骨架屏的最佳实践。
*   **[状态管理 (state-management.mdc)](mdc:state-management.mdc)**: 使用 Pinia 进行状态管理的规范和建议。
*   **[样式与布局 (styling.mdc)](mdc:styling.mdc)**: UnoCSS 原子化 CSS、响应式设计和常用布局模式的应用指南。
*   **[路由 (routing.mdc)](mdc:routing.mdc)**: 基于文件的路由 (`vite-plugin-pages`) 配置和路由参数的获取方法。
*   **[组合式函数 (composables.mdc)](mdc:composables.mdc)**: 编写和使用 `composables` 抽取可复用逻辑，集成 VueUse。

## 质量与优化

*   **[性能优化 (performance.mdc)](mdc:performance.mdc)**: 应用前端性能优化的核心策略，提升用户体验。

## 重要配置文件参考

*   **[UnoCSS 配置 (uno.config.ts)](mdc:../uno.config.ts)**: 查看或修改原子化 CSS 规则和预设。
*   **[Vite 配置 (vite.config.ts)](mdc:../vite.config.ts)**: 理解项目构建和开发服务器配置。
*   **[HTTP 工具 README](mdc:../src/utils/http/README.md)**: 查阅 HTTP 请求工具的详细说明。

> 注意: `mdc:` 链接指向规则文件，其他链接指向项目中的实际文件。