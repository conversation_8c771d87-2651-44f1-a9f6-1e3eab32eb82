<script setup lang="ts">
// 定义props
defineProps({
  userInfo: {
    type: Object,
    required: true
  },
  isHeaderCollapsed: {
    type: Boolean,
    default: false
  },
  headerCollapseRatio: {
    type: Number,
    default: 0
  },
  headerHeight: {
    type: String,
    default: '160px'
  },
  appBarStyle: {
    type: String,
    default: () => ('linear-gradient(180deg, #5D4E43 0%, #937A67 78%, #685749 100%)')
  },
  vipTagImage: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <var-app-bar
    :style="{
      height: headerHeight
    }"
    :color="appBarStyle"
    fixed
    placeholder
    class="shadow-none app-bar-container"
  >
    <template #left>
      <!-- logo展示 -->
      <div class="flex items-center">
        <img 
          v-if="userInfo.gameImg" 
          :src="userInfo.gameImg" 
          alt="游戏Logo" 
          class="h-8 object-contain"
        />
        <span v-else class="text-lg font-bold text-white"></span>
      </div>
    </template>

    <template #right>
      <var-button round text color="transparent" text-color="#fff">
        <var-icon name="plus-circle-outline" :size="24" />
      </var-button>
    </template>

    <template #content>
      <div
        class="header-content-wrapper flex items-center px-4 justify-between transition-opacity"
        :style="{ 
          opacity: 1 - headerCollapseRatio,
          minHeight: `${100 - (100 * headerCollapseRatio)}px`,
          overflow: 'hidden'
        }"
        style="will-change: opacity, height;"
      >
        <div class="user-info flex flex-1 gap-3 items-center" v-if="userInfo.gameImg">
          <div class="user-avatar">
            <var-avatar
              :size="72"
              shape="circle"
              :src="userInfo.gameImg"
            />
          </div>
          <div class="flex flex-col gap-1 pb-1">
            <h3
              class="font-600 text-title leading-tight m-0 transition-colors duration-300 text-white"
            >
              {{ userInfo.name }}
            </h3>
            <p
              v-if="userInfo.uidShow"
              class="font-medium text-md leading-tight text-gray-300 m-0 font-din p-0"
            >
              UID:{{ userInfo.uidShow }}
            </p>
            <p
              v-else
              class="font-medium text-md leading-tight m-0 opacity-0 h-2 text-gray-300"
            >
              &nbsp;
            </p>
          </div>
        </div>
        <div class="vip-tag h-22 w-22" v-if="vipTagImage">
          <div class="vip-tag-item">
            <div class="vip-tag-item-icon">
              <img :src="vipTagImage" alt="vip" class="w-full h-full">
            </div>
          </div>
        </div>
      </div>
    </template>
  </var-app-bar>
</template>

<style scoped>
.app-bar-container {
  transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
}

.header-content-wrapper {
  transition: all 0.3s cubic-bezier(0.33, 1, 0.68, 1);
  will-change: opacity, height;
}

.vip-tag-item {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style> 