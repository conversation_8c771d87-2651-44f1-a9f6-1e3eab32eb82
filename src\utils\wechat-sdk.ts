import { wechat<PERSON><PERSON> } from '@/api'

interface WxConfig {
  debug: boolean
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  jsApiList: string[]
}

class WechatSDK {
  private isReady: boolean = false
  private isInitializing: boolean = false
  private initPromise: Promise<void> | null = null
  private readyCallbacks: (() => void)[] = []

  async init(jsApiList: string[] = ['updateAppMessageShareData', 'updateTimelineShareData']): Promise<void> {
    if (this.isReady) {
      return
    }

    if (this.isInitializing) {
      return this.initPromise
    }

    this.isInitializing = true
    this.initPromise = this.initializeSDK(jsApiList)

    try {
      await this.initPromise
      this.isReady = true
      this.executeReadyCallbacks()
    }
    catch (error) {
      console.error('微信JS SDK 初始化失败', error)
    }
    finally {
      this.isInitializing = false
    }
  }

  private async initializeSDK(jsApiList: string[]): Promise<void> {
    const url = window.location.href.split('#')[0]
    const urlWithoutQuery = url.split('?')[0]
    const { signature, nonceStr, timestamp } = await wechatApi.getSdkSignature({ url: urlWithoutQuery })

    const config: WxConfig = {
      // eslint-disable-next-line node/prefer-global/process
      debug: process.env.NODE_ENV === 'development',
      appId: import.meta.env.VITE_APP_WECHAT_APP_ID,
      timestamp,
      nonceStr,
      signature,
      jsApiList,
    }

    await this.configWx(config)
  }

  private configWx(config: WxConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      this.ensureWxLoaded(() => {
        wx.config(config)
        wx.ready(() => {
          resolve()
          this.executeReadyCallbacks()
        })
        wx.error((res: any) => reject(res))
      })
    })
  }

  private ensureWxLoaded(callback: () => void) {
    if (typeof wx !== 'undefined') {
      callback()
    }
    else {
      setTimeout(() => this.ensureWxLoaded(callback), 50)
    }
  }

  async ready(callback: () => void): Promise<void> {
    if (this.isReady) {
      callback()
    }
    else {
      this.readyCallbacks.push(callback)
      if (!this.isInitializing) {
        await this.init()
      }
    }
  }

  private executeReadyCallbacks() {
    this.readyCallbacks.forEach(callback => callback())
    this.readyCallbacks = []
  }
}

export const wechatSDK = new WechatSDK()
