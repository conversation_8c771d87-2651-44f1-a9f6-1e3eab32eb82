<script setup lang="ts">
import { Popup, Divider, Icon, Steps, Step, Button } from '@varlet/ui'
import { ref, watchEffect, computed } from 'vue'

interface TicketProgress {
  status: 'submitted' | 'processing' | 'completed'
  time: string
  remark?: string
}

interface CustomField {
  key: string
  value: string
}

export interface TicketDetail {
  id: string
  title: string
  content: string
  status: 'processing' | 'completed'
  createTime: string
  progress: TicketProgress[]
  customFields?: CustomField[]
}

const props = defineProps<{
  visible: boolean
  ticket?: TicketDetail
}>()

const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'urge'): void
}>()

// 使用计算属性处理双向绑定
const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const currentStep = ref(0)
const progressSteps = ref<Array<{ 
  label: string, 
  status: string, 
  time?: string, 
  remark?: string 
}>>([
  { label: '提交工单', status: 'default', remark: '工单创建成功，已交由专属客服处理。' },
  { label: '处理中', status: 'default', remark: '专属客服正在处理您的请求，请稍作等待。' },
  { label: '已完成', status: 'default', remark: '工单处理已完成，如有其他问题，欢迎联系客服。' }
])

// 更新进度状态
watchEffect(() => {
  if (!props.ticket) return

  // 重置状态
  progressSteps.value = [
    { label: '提交工单', status: 'default', remark: '工单创建成功，已交由专属客服处理。' },
    { label: '处理中', status: 'default', remark: '专属客服正在处理您的请求，请稍作等待。' },
    { label: '已完成', status: 'default', remark: '工单处理已完成，如有其他问题，欢迎联系客服。' }
  ]
  
  currentStep.value = 0

  if (props.ticket.progress && props.ticket.progress.length > 0) {
    // 获取最新的进度状态
    const latestProgress = props.ticket.progress[props.ticket.progress.length - 1]
    
    // 设置当前步骤
    switch (latestProgress.status) {
      case 'submitted':
        currentStep.value = 0
        break
      case 'processing':
        currentStep.value = 1
        break
      case 'completed':
        currentStep.value = 2
        break
    }

    // 更新进度条状态
    props.ticket.progress.forEach(item => {
      switch (item.status) {
        case 'submitted':
          progressSteps.value[0].status = 'completed'
          progressSteps.value[0].time = item.time
          if (item.remark) progressSteps.value[0].remark = item.remark
          break
        case 'processing':
          progressSteps.value[0].status = 'completed'
          progressSteps.value[1].status = 'current'
          progressSteps.value[1].time = item.time
          if (item.remark) progressSteps.value[1].remark = item.remark
          break
        case 'completed':
          progressSteps.value[0].status = 'completed'
          progressSteps.value[1].status = 'completed'
          progressSteps.value[2].status = 'completed'
          progressSteps.value[2].time = item.time
          if (item.remark) progressSteps.value[2].remark = item.remark
          break
      }
    })
  }
})

const handleClose = () => {
  localVisible.value = false
}

const handleUrge = () => {
  emit('urge')
}
</script>

<template>
  <var-popup 
    v-model:show="localVisible"
    position="bottom" 
    class="ticket-detail-popup"
    :overlay="true"
    fullscreen
  >
    <div class="ticket-detail p-4 min-h-screen">
      <!-- 头部 -->
      <div class="flex justify-between items-center mb-6 relative">
        <var-button round text class="ml-auto" @click="handleClose">
          <var-icon name="window-close" size="24" />
        </var-button>
      </div>
      
      <!-- 顶部信息 -->
      <div class="flex justify-between items-center mb-4">
        <span class="text-gray-500 text-sm">服务单号：{{ ticket?.id }}</span>
        <div 
          v-if="ticket"
          class="status-tag px-3 text-xs text-white rounded-full py-1"
          :class="ticket.status === 'processing' ? 'bg-yellow' : 'bg-green'"
        >
          {{ ticket.status === 'processing' ? '处理中' : '已完结' }}
        </div>
      </div>
      
      <!-- 标题 -->
      <h2 class="text-lg font-medium mb-4">{{ ticket?.title }}</h2>
    
      <!-- 进度条 -->
      <div class="progress-timeline p-4">
        <var-steps :active="currentStep" direction="vertical" active-color="#ffc82c">
          <var-step 
            v-for="(step, index) in progressSteps" 
            :key="index"
          >
            <template #default>
              <div class="step-content w-full ml-4">
                <div class="flex justify-between items-center">
                  <div class="step-label text-base truncate max-w-[70%]">
                    {{ step.label }}
                  </div>
                  <div v-if="step.time" class="step-time text-xs text-gray-400">
                    {{ step.time }}
                  </div>
                </div>
                <div v-if="step.remark" class="step-remark text-sm text-gray-400 mt-1 line-clamp-1">
                  {{ step.remark }}
                </div>
              </div>
            </template>
          </var-step>
        </var-steps>
      </div>

      <!-- 工单内容 -->
      <div class="content-box bg-gray-100 p-3 rounded-4 mb-6">
        <p class="text-gray-600">{{ ticket?.content }}</p>
      </div>
      
      <!-- 自定义字段 -->
      <div v-if="ticket?.customFields && ticket.customFields.length > 0" class="custom-fields bg-gray-100 p-3 rounded-4 mb-6">
        <div class="grid grid-cols-2 gap-2">
          <template v-for="(field, index) in ticket.customFields" :key="index">
            <div class="field-label text-sm text-gray-500">{{ field.key }}</div>
            <div class="field-value text-sm">{{ field.value }}</div>
          </template>
        </div>
      </div>
      
      <!-- 底部信息 -->
      <div class="flex justify-between items-center mt-6">
        <span class="text-gray-400 text-sm">{{ ticket?.createTime }}</span>
        <button 
          v-if="ticket?.status === 'processing'"
          class="urge-btn border border-black px-4 py-1.5 rounded-full text-sm"
          @click="handleUrge"
        >
          催单
        </button>
      </div>
    </div>
  </var-popup>
</template>

<style scoped>
.ticket-detail {
  max-width: 600px;
  margin: 0 auto;
}

:deep(.var-step) {
  min-height: 64px;
  padding-bottom: 8px;
}

:deep(.var-step-content) {
  margin-top: -4px;
}

:deep(.var-step-vertical .var-step-line) {
  left: 6px;
}

:deep(.var-step-vertical .var-step-dot-container) {
  height: 12px;
  width: 12px;
  line-height: 12px;
}

:deep(.var-step-dot) {
  height: 12px;
  width: 12px;
}

.step-content {
  max-height: 48px;
  overflow: hidden;
}
.progress-timeline :deep(.var-step__vertical-tag),
.progress-timeline :deep(.var-step__vertical-line) {
  margin-top: 14px;
}
.progress-timeline :deep(.var-step__vertical-content) {
  width: 100%;
}

</style> 