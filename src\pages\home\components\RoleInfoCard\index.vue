<template>
  <div class="relative w-full h-22 rounded-xl overflow-hidden shadow-[0px_1px_4px_0px_rgba(0,0,0,0.15)]">
    <!-- 背景图片 -->
    <div class="absolute top-0 left-0 w-full h-full z-[1]">
      <img 
        v-if="game?.iconLogo" 
        :src="game.iconLogo" 
        :alt="game.name"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full bg-gradient-to-br from-[#370369] to-[#19032D]"></div>
      <!-- 渐变遮罩 -->
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-[rgba(19,21,40,0.8)] to-[rgba(19,21,40,0)]"></div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="relative z-[2] flex items-center justify-between p-4 h-full">
      <!-- 左侧用户信息区域 -->
      <div class="flex items-center gap-2 flex-1">
        <!-- 方形Logo容器 -->
        <div class="relative w-14 h-14 flex-shrink-0">
          <!-- 主Logo -->
          <div class="w-10 h-10 rounded-lg overflow-hidden">
            <img 
              v-if="binding?.avatar" 
              :src="binding.avatar" 
              :alt="binding.roleName"
              class="w-full h-full object-cover"
            />
            <div v-else class="w-full h-full bg-[rgba(255,255,255,0.1)] flex items-center justify-center">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/>
              </svg>
            </div>
          </div>
          <!-- 装饰边框 -->
          <div class="absolute -top-1 -left-1 w-15 h-15 rounded-lg border border-[rgba(255,255,255,0.2)]"></div>
        </div>
        
        <!-- 用户信息 -->
        <div class="flex flex-col gap-1">
          <!-- 昵称 -->
          <div class="text-white text-base font-normal leading-[1.22] whitespace-nowrap overflow-hidden text-ellipsis">
            {{ binding?.roleName || '用户昵称' }}
          </div>
          <!-- ID信息 -->
          <div class="text-[#7E7E7E] text-xs leading-[1.15]" v-if="binding?.roleIdShow">
            ID: {{ binding.roleIdShow }}
          </div>
        </div>
      </div>
      
      <!-- 右侧角色管理按钮 -->
      <button 
        class="px-2.5 py-1.5 bg-[#D3FF55] border-none rounded-[19px] text-[#1C1C1C] text-xs font-normal leading-[1.4] cursor-pointer transition-all duration-200 ease-in-out flex-shrink-0 hover:bg-[#E5FF7A] hover:-translate-y-0.5 active:translate-y-0" 
        @click="handleManageClick"
      >
        角色管理
      </button>
    </div>
  </div>

  <!-- 角色管理弹窗 -->
  <RoleManagementModal 
    v-model:visible="showManagementModal"
    :game-id="game?.gameId"
    @role-changed="handleRoleChanged"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { Game, UserBinding } from '@/stores/game'
import RoleManagementModal from '../RoleManagementModal/index.vue'

interface Props {
  game?: Game | null
  binding?: UserBinding | null
}

interface Emits {
  (e: 'manage'): void
  (e: 'role-changed'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 弹窗显示状态
const showManagementModal = ref(false)

const handleManageClick = () => {
  showManagementModal.value = true
  emit('manage')
}

const handleRoleChanged = () => {
  emit('role-changed')
}
</script>