import * as Sentry from '@sentry/vue';
import { BrowserTracing } from '@sentry/tracing';
import type { App } from 'vue';
import type { Router } from 'vue-router';
import { initSentryHttpMonitoring } from './sentry';

interface SentryPluginOptions {
  // Sentry DSN
  dsn: string;
  // Vue Router实例
  router?: Router;
  // 环境标识
  environment?: string;
  // 是否启用HTTP错误监控
  enableHttpMonitoring?: boolean;
  // HTTP错误监控配置
  httpMonitoringOptions?: {
    // 忽略的URL正则表达式数组
    ignoreUrls?: RegExp[];
    // 忽略的错误状态码
    ignoreStatuses?: number[];
    // 忽略的业务错误码
    ignoreBusinessCodes?: number[];
    // 采样率 (0-1.0), 1表示全部上报
    sampleRate?: number;
    // 自定义标签
    tags?: Record<string, string>;
    // 是否在控制台也打印错误
    logToConsole?: boolean;
  };
  // Sentry Tracing采样率
  tracesSampleRate?: number;
}

export default {
  install: (app: App, options: SentryPluginOptions) => {
    if (!options.dsn) {
      // 未设置DSN时不初始化Sentry
      console.warn('[Sentry Plugin] 未提供Sentry DSN，跳过初始化');
      return;
    }
    
    // 基础Sentry配置
    const sentryOptions: any = {
      app,
      dsn: options.dsn,
      environment: options.environment || process.env.NODE_ENV || 'development',
      integrations: [],
      tracesSampleRate: options.tracesSampleRate || 1.0,
      
      // 建议生产环境关闭debug
      debug: process.env.NODE_ENV !== 'production',
    };
    
    // 如果提供了路由，添加路由集成
    if (options.router) {
      sentryOptions.integrations.push(
        new BrowserTracing({
          routingInstrumentation: Sentry.vueRouterInstrumentation(options.router),
          tracingOrigins: ['localhost', /^\//],
        })
      );
    }
    
    // 初始化Sentry
    Sentry.init(sentryOptions);
    
    // 将Sentry暴露给Vue实例
    app.config.globalProperties.$sentry = Sentry;
    app.provide('sentry', Sentry);
    
    // 启用HTTP错误监控
    if (options.enableHttpMonitoring !== false) {
      const httpMonitor = initSentryHttpMonitoring({
        ...options.httpMonitoringOptions,
        tags: {
          app: 'wechat-web',
          ...(options.httpMonitoringOptions?.tags || {}),
        },
      });
      
      console.info('[Sentry Plugin] Sentry 和 HTTP 错误监控已初始化');
      
      // 为app提供卸载方法
      const originalUnmount = app.unmount;
      app.unmount = () => {
        httpMonitor.uninstall();
        originalUnmount();
      };
    }
  },
}; 