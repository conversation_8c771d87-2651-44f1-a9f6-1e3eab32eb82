import { ref, computed, onMounted } from 'vue'
import { useGameStore } from '@/stores/game'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import { homeApi } from '@/api/home'
import type { GameDataResponse, HomeConfigResponse } from '@/api/home/<USER>'

export function useHomeData() {
  const gameStore = useGameStore()
  const userStore = useUserStore()
  const themeStore = useThemeStore()

  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const currentGame = computed(() => {
    return gameStore.getGameById(gameStore.activeGameId)
  })

  const wechatUserInfo = computed(() => userStore.wechatUserInfo)

  const currentTheme = computed(() => {
    return themeStore.getThemeByGameId()
  })

  const banners = computed(() => {
    return currentTheme.value?.banners || []
  })

  // 检查是否处于无状态（没有绑定角色）
  const isEmptyState = computed(() => {
    return !gameStore.activeRole
  })


  return {
    // 状态
    loading,
    error,
    
    // 数据
    currentGame,
    wechatUserInfo,
    currentTheme,
    banners,
    isEmptyState,
  }
}