<script setup lang="ts">
// 骨架屏组件，用于加载状态
</script>

<template>
  <div class="binding-skeleton-container h-full w-full bg-gray-50">
    <!-- Logo骨架 -->
    <div class="logo-skeleton h-[180px] flex items-center justify-center">
      <div class="w-[120px] h-[120px] bg-gray-200 rounded-lg animate-pulse"></div>
    </div>

    <!-- 表单内容骨架 -->
    <div class="form-skeleton px-8">
      <!-- 标题骨架 -->
      <div class="title-skeleton flex justify-center mb-6">
        <div class="w-32 h-6 bg-gray-200 rounded animate-pulse"></div>
      </div>

      <!-- 表单字段骨架 -->
      <div class="fields-skeleton space-y-4">
        <!-- 字段1 -->
        <div class="field-skeleton">
          <div class="w-20 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div class="w-full h-12 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>

        <!-- 字段2 -->
        <div class="field-skeleton">
          <div class="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div class="w-full h-12 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>

        <!-- 字段3（可选） -->
        <div class="field-skeleton">
          <div class="w-24 h-4 bg-gray-200 rounded animate-pulse mb-2"></div>
          <div class="w-full h-12 bg-gray-200 rounded-lg animate-pulse"></div>
        </div>
      </div>

      <!-- 按钮骨架 -->
      <div class="button-skeleton mt-8">
        <div class="w-full h-12 bg-gray-200 rounded-xl animate-pulse"></div>
      </div>
    </div>

    <!-- 底部链接骨架 -->
    <div class="footer-skeleton flex items-center justify-center py-6 mt-8">
      <div class="w-32 h-4 bg-gray-200 rounded animate-pulse"></div>
    </div>
  </div>
</template>

<style scoped>
.binding-skeleton-container {
  min-height: 100vh;
}

/* 自定义动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 渐变动画效果 */
.field-skeleton:nth-child(1) .animate-pulse {
  animation-delay: 0s;
}

.field-skeleton:nth-child(2) .animate-pulse {
  animation-delay: 0.2s;
}

.field-skeleton:nth-child(3) .animate-pulse {
  animation-delay: 0.4s;
}

.button-skeleton .animate-pulse {
  animation-delay: 0.6s;
}

.footer-skeleton .animate-pulse {
  animation-delay: 0.8s;
}

/* 更平滑的骨架屏效果 */
.animate-pulse {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
