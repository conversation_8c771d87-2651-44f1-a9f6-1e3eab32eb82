<script setup lang="ts">
import TicketTabs from './components/TicketTabs/index.vue'
import TicketCard from './components/TicketCard/index.vue'
import TicketDetail from './components/TicketDetail/index.vue'
import LoadingState from './components/LoadingState/index.vue'
import EmptyState from './components/EmptyState/index.vue'
import { useTickets } from './composables/useTickets'
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useTicketListService } from './api'

const route = useRoute()
const router = useRouter()

const { 
  tickets, 
  isLoading, 
  activeTab, 
  filteredTickets, 
  selectedTicket,
  detailVisible,
  changeTab, 
  fetchTickets,
  showDetail,
  urgeTicket
} = useTickets()

// 使用工单列表服务
const { ticketsData, ticketsLoading, fetchTickets: useTicketListServiceFetchTickets } = useTicketListService()

// 渲染时获取工单列表
onMounted(() => {
  fetchTickets()
})

// 跳转到创建工单页面
const createTicket = () => {
  router.push('/contact/ticket/create')
}

// 查看工单详情
const viewTicket = (id: string) => {
  router.push(`/contact/ticket/${id}`)
}

// 在query中查找是否有ticketId
onMounted(async () => {
    // 进入页面时获取工单列表
  await fetchTickets()
  const ticketId = route.query.ticketId
  if (ticketId) {
    const ticket = tickets.value.find(ticket => ticket.id === ticketId)
    if (ticket) {
      showDetail(ticket)
    }
  }
})
</script>

<template>
  <div class="ticket-page px-4 py-6">
    
    <!-- 选项卡部分 -->
    <TicketTabs :activeTab="activeTab" @change="changeTab" />
    
    <!-- 加载状态 -->
    <LoadingState v-if="isLoading" />
    
    <!-- 工单列表 -->
    <div v-else class="ticket-list mt-4">
      <!-- 空状态 -->
      <EmptyState v-if="filteredTickets.length === 0" />
      
      <!-- 工单列表项 -->
      <div v-else class="space-y-4">
        <TicketCard
          v-for="ticket in filteredTickets"
          :key="ticket.id"
          :ticket="ticket"
          @click="() => showDetail(ticket)"
        />
      </div>
    </div>
    
    <!-- 工单详情弹窗 -->
    <TicketDetail 
      v-model:visible="detailVisible"
      :ticket="selectedTicket"
      @urge="() => urgeTicket(selectedTicket)"
    />
  </div>
</template>

<style scoped>
.ticket-page {
  max-width: 800px;
}
</style>

<route lang="json">
{
	"meta": {
		"title": "服务进度",
		"layout": "backHeader"
	}
}
</route>
