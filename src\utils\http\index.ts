/* 简化的HTTP封装 - 统一认证和公共API */
// 功能: 提供统一的HTTP客户端，支持auth参数控制认证

import { HttpClient } from './httpClient'
import type { HttpClientConfig } from './config'
import type { ApiResponse } from './types'

export * from './types'
export * from './eventBus'
export * from './config'

// 默认全局实例
export const http = new HttpClient()

// 创建自定义实例
export const createHttp = (config: Partial<HttpClientConfig> = {}) => new HttpClient(config)

// 统一请求方法 - 类似原生axios的使用方式
export const request = <R = any>(config: {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  params?: any
  headers?: any
  auth?: boolean  // 改为更直观的auth参数，默认true需要认证
  timeout?: number
  [key: string]: any
}): Promise<ApiResponse<R>> => {
  // 转换auth参数为内部的skipAuth
  const { auth = true, ...restConfig } = config
  return http.request<R>({
    ...restConfig,
    skipAuth: !auth  // auth=false时skipAuth=true
  })
}