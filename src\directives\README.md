# 全局自定义指令

本项目采用统一的自定义指令注册机制，所有指令集中管理，并通过`src/directives/index.ts`自动注册。

## 目录结构

```
src/directives/
├── index.ts         # 入口文件，自动注册所有指令
├── track.ts         # v-track 追踪指令
├── README.md        # 说明文档
└── ...              # 其他自定义指令
```

## 添加新指令

要添加新的自定义指令，只需三个步骤：

1. 在`src/directives/`目录下创建新的指令文件，例如`my-directive.ts`
2. 导出指令对象和设置函数
3. 在`index.ts`中导入并注册新指令

### 指令实现模板

```typescript
// src/directives/my-directive.ts
import type { Directive, App } from 'vue'

// 指令实现
const myDirective: Directive = {
  mounted(el, binding) {
    // 指令逻辑
  },
  updated(el, binding) {
    // 更新逻辑
  }
}

// 安装函数
export function setupMyDirective(app: App) {
  app.directive('my-directive', myDirective)
}

export default myDirective
```

### 在index.ts中注册

```typescript
// src/directives/index.ts
import { setupMyDirective } from './my-directive'

export function setupDirectives(app: App): void {
  // ... 其他指令
  setupMyDirective(app)
}
```

## 当前可用指令

### v-track

追踪用户交互事件，自动发送到Clarity分析平台。

```vue
<!-- 基本用法 -->
<button v-track>默认追踪</button>

<!-- 自定义事件名称 -->
<button v-track="'提交订单'">提交</button>

<!-- 追踪表单提交 -->
<form v-track:submit="'注册表单'">...</form>

<!-- 追踪元素可见性 -->
<div v-track:view="'重要内容'">...</div>

<!-- 追踪鼠标悬停 -->
<div v-track:hover="'产品卡片'">...</div>
```

更多详情请查看对应指令文件的文档。 