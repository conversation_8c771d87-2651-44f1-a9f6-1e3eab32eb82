import type { App } from 'vue'
import ClarityPlugin from '@/utils/clarity'
import * as clarity from '@microsoft/clarity'

// 构建自动追踪路由的函数
function setupRouteTracking(app: App) {
  // 获取路由实例
  const router = app.config.globalProperties.$router
  if (router) {
    router.afterEach((to) => {
      // 使用包中的方法记录页面访问
      clarity.event('page_view', {
        path: to.path,
        name: to.name,
        timestamp: Date.now()
      })
    })
  }
}

export default (app: App) => {
  // 注册Clarity插件
  app.use(ClarityPlugin, {
    projectId: import.meta.env.VITE_CLARITY_PROJECT_ID || '',
    productionOnly: true,
    config: {
      // 延迟加载以优先处理核心功能
      delay: 1500
    }
  })
  
  // 设置路由追踪
  setupRouteTracking(app)
} 