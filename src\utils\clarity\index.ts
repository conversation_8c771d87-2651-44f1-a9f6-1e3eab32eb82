import type { App } from 'vue'
import * as clarity from '@microsoft/clarity'
import type { ClarityConfig } from '@microsoft/clarity'

export interface ClarityOptions {
  // Clarity项目ID
  projectId: string
  // 是否仅在生产环境启用
  productionOnly?: boolean
  // 自定义配置项
  config?: Partial<ClarityConfig>
}

// 初始化Clarity
function initClarity(projectId: string, config?: Partial<ClarityConfig>): void {
  if (!projectId) {
    console.warn('[Clarity] 未提供项目ID，跳过初始化')
    return
  }

  try {
    // 使用npm包初始化
    clarity.init({
      projectId,
      track: true,
      // 合并默认配置与用户配置
      ...config
    });
    console.info('[Clarity] 已初始化，项目ID:', projectId)
  } catch (error) {
    console.error('[Clarity] 初始化失败:', error)
  }
}

// Vue插件实现
export default {
  install: (app: App, options: ClarityOptions) => {
    // 检查是否应该加载Clarity
    const isProduction = import.meta.env.PROD
    if (options.productionOnly && !isProduction) {
      console.info('[Clarity] 仅在生产环境启用，当前为开发环境')
      return
    }

    // 配置延迟加载
    const delay = options.config?.delay || 0
    setTimeout(() => {
      initClarity(options.projectId, options.config)
    }, delay)

    // 提供给组件使用
    const clarityService = {
      // 手动追踪事件
      trackEvent(eventName: string, metadata?: Record<string, any>) {
        clarity.event(eventName, metadata)
      },
      // 设置自定义用户ID
      setUserId(userId: string) {
        clarity.identify(userId)
      },
      // 设置自定义会话标签
      setTag(key: string, value: string) {
        clarity.set(key, value)
      },
      // 暂停追踪
      pause() {
        clarity.pause()
      },
      // 恢复追踪
      resume() {
        clarity.resume()
      },
      // 原始clarity对象
      clarity
    }
    
    // 通过provide和全局属性提供
    app.provide('clarity', clarityService)
    app.config.globalProperties.$clarity = clarityService
  }
}

// 类型扩展
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $clarity: {
      trackEvent(eventName: string, metadata?: Record<string, any>): void
      setUserId(userId: string): void
      setTag(key: string, value: string): void
      pause(): void
      resume(): void
      clarity: typeof clarity
    }
  }
} 