import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import { useApiRequest } from '@/composables/useApiRequest'
import { rechargeApi } from '@/api'
import type { VipTheme } from '@/api/recharge/types'

/**
 * 充值页面配置相关Hook
 */
export function useRechargeConfig() {
  const userStore = useUserStore()
  const themeStore = useThemeStore()
  const gameId = computed(() => String(userStore.activeGameId || ''))

  // 获取VIP配置
  const {
    data: vipConfigData,
    loading: vipConfigLoading,
    run: fetchVipConfig
  } = useApiRequest(
    () => rechargeApi.getVipConfig(),
    {
      manual: true
    }
  )
  
  // 获取当前用户VIP等级
  const {
    data: vipLevelData,
    loading: vipLevelLoading,
    run: fetchVipLevel
  } = useApiRequest(
    () => rechargeApi.getUserVipLevel(),
    {
      manual: true
    }
  )
  
  // 获取轮播配置
  const {
    data: swipeConfigData,
    loading: swipeConfigLoading,
    run: fetchSwipeConfig
  } = useApiRequest(
    () => rechargeApi.getSwipeConfig(gameId.value),
    {
      manual: true,
      refreshDeps: [gameId]
    }
  )
  
  // 计算属性
  // 获取当前VIP主题
  const currentVipTheme = computed<VipTheme | null>(() => {
    if (!vipConfigData?.value?.data || !vipLevelData?.value?.data) return null
    
    const level = vipLevelData.value.data.level || 0
    return vipConfigData.value.data.find(theme => theme.level === level) || (vipConfigData.value.data[0] || null)
  })
  
  // 当前游戏的轮播项
  const currentSwipeItems = computed(() => {
    if (!swipeConfigData?.value?.data) return []
    
    // 尝试获取当前游戏的轮播配置
    const currentGameConfig = swipeConfigData.value.data[gameId.value]
    
    // 如果没有当前游戏的配置，则使用默认配置
    if (!currentGameConfig) {
      const defaultConfig = swipeConfigData.value.data['default']
      return defaultConfig?.swipeItems || []
    }
    
    return currentGameConfig.swipeItems || []
  })
  
  const vipTagImage = computed(() => 
    currentVipTheme.value?.tagImage || ''
  )

  const appBarStyle = computed(() =>
    currentVipTheme.value?.appBarColor ||
    'linear-gradient(180deg, #5D4E43 0%, #937A67 78%, #685749 100%)'
  )

  const swipeItems = computed(() => 
    currentSwipeItems.value || []
  )

  // 计算当前VIP等级
  const vipLevel = computed(() => 
    vipLevelData.value?.data?.level || 0
  )

  // 总体加载状态
  const isLoading = computed(() => {
    return Boolean(vipConfigLoading.value) || 
           Boolean(vipLevelLoading.value) || 
           Boolean(swipeConfigLoading.value)
  })

  // 获取所有配置
  const fetchRechargeConfig = async () => {
    await Promise.all([
      fetchVipConfig(),
      fetchVipLevel(),
      fetchSwipeConfig()
    ])
  }

  return {
    vipLevel,
    vipTagImage,
    appBarStyle,
    swipeItems,
    isLoading,
    fetchRechargeConfig
  }
} 