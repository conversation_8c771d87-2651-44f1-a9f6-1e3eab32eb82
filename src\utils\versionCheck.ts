import { ref } from 'vue'
import { Dialog, Snackbar } from '@varlet/ui'
import '@varlet/ui/es/dialog/style/index'
import '@varlet/ui/es/snackbar/style/index'

// 版本检查配置
interface VersionCheckOptions {
  /**
   * 检查间隔时间（毫秒）
   * @default 10000
   */
  interval?: number
  
  /**
   * 版本文件路径
   * @default '/version.json'
   */
  versionUrl?: string
  
  /**
   * 是否在控制台输出调试信息
   * @default false
   */
  debug?: boolean
}

// 默认配置
const DEFAULT_OPTIONS: VersionCheckOptions = {
  interval: 10 * 1000,
  versionUrl: '/version.json',
  debug: false
}

function showUpdateConfirm() {
  Dialog({
    title: '系统更新',
    message: '系统有新版本可用，是否立即更新？',
    confirmButton: true,
    cancelButton: true,
    confirmButtonText: '立即更新',
    cancelButtonText: '取消',
    confirmButtonTextColor: '#fff',
    confirmButtonColor: '#2979ff',
    onConfirm: () => {
      window.location.reload()
    }
  })
}

let currentVersion = ''
let lastCheckTime = 0
let options = DEFAULT_OPTIONS
let checkIntervalId: number | null = null

async function checkVersion() {
  const now = Date.now()
  if (now - lastCheckTime < options.interval!) {
    return
  }

  try {
    const response = await fetch(`${options.versionUrl}?t=${now}`)
    
    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.status}`)
    }
    
    const data = await response.json()
    const { version } = data
    
    if (options.debug) {
      console.log('[版本检查]', { 当前版本: currentVersion, 服务器版本: version, 数据: data })
    }

    if (currentVersion && currentVersion !== version) {
      showUpdateConfirm()
    }

    currentVersion = version
    lastCheckTime = now
  }
  catch (error) {
    if (options.debug) {
      console.error('检查版本更新失败:', error)
      Snackbar.error('版本检查失败，请检查网络连接')
    }
  }
}

/**
 * 初始化版本检查功能
 * @param customOptions 自定义配置选项
 */
export function initVersionCheck(customOptions: Partial<VersionCheckOptions> = {}) {
  // 合并配置
  options = { ...DEFAULT_OPTIONS, ...customOptions }
  
  // 初始检查
  checkVersion()

  // 注册全局点击事件
  document.addEventListener('click', () => {
    checkVersion()
  }, { passive: true })

  // 监听其他用户活动事件
  const userActivityEvents = ['keydown', 'mousemove', 'scroll']
  let activityTimeout: number | null = null

  const handleUserActivity = () => {
    if (activityTimeout) {
      clearTimeout(activityTimeout)
    }

    activityTimeout = window.setTimeout(() => {
      checkVersion()
    }, 1000)
  }

  userActivityEvents.forEach((event) => {
    document.addEventListener(event, handleUserActivity, { passive: true })
  })
  
  // 设置定期检查（备用方案）
  if (checkIntervalId) {
    clearInterval(checkIntervalId)
  }
  
  checkIntervalId = window.setInterval(checkVersion, options.interval)
  
  if (options.debug) {
    console.log('[版本检查] 初始化完成', options)
  }
  
  // 返回清理函数
  return () => {
    if (checkIntervalId) {
      clearInterval(checkIntervalId)
      checkIntervalId = null
    }
    
    userActivityEvents.forEach((event) => {
      document.removeEventListener(event, handleUserActivity)
    })
    
    document.removeEventListener('click', checkVersion)
    
    if (options.debug) {
      console.log('[版本检查] 已停止')
    }
  }
}
