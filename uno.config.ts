import {
	defineConfig,
	presetAttributify,
	presetIcons,
	presetTypography,
	presetUno,
	transformerVariantGroup,
	transformerDirectives,
} from 'unocss'

import presetAutoprefixer from './presets/autoprefixer'
import { presetVarlet } from '@varlet/preset-unocss'

export default defineConfig({
	transformers: [transformerDirectives(), transformerVariantGroup()],
	presets: [
		presetAttributify(),
		presetIcons({
			autoInstall: true,
		}),
		presetUno(),
		presetTypography(),
		presetAutoprefixer(),
		presetVarlet(),
	],
	theme: {
		colors: {
			// 主色
			primary: '#D3FF55',

			// 通用色
			white: '#FFFFFF',
			'white-1': '#F8F8F8',
			dark: '#1C1C1C',
			'dark-1': '#333333',
			'dark-2': '#7E7E7E',

			// 灰度
			'grey-01': '#DEDEDE',
			'grey-02': '#C7C7CC',
			'grey-03': '#AEAEB2',
			'grey-04': '#8E8E93',
			'grey-05': '#636366',
			'grey-06': '#3C3C3E',
			'grey-07': '#111111',

			// 功能色
			highlight: '#009FF5',
			error: '#FF4444',
			success: '#00C800',
		},

		fontFamily: {
			cn: 'PingFangSC, Microsoft YaHei, -apple-system, BlinkMacSystemFont, sans-serif',
			din: 'DIN, -apple-system, BlinkMacSystemFont, sans-serif',
		},

		fontSize: {
			// 特殊类标题
			title: '22px',
			// 用户昵称
			h1: '20px',
			// 标题/重要信息
			h2: '18px',
			// 正文
			text: '16px',
			// 输入显示
			input: '14px',
			// 说明性/提示文字
			caption: '12px',
			// 说明性/提示文字(小)
			'caption-sm': '10px',
			// 英文字体尺寸
			lg: '18px',
			md: '14px',
			sm: '12px',
		},

		fontWeight: {
			regular: '400',
			medium: '500',
		},

		// 间距规范
		spacing: {
			'0': '0px',
			'1': '4px',
			'2': '8px',
			'3': '12px',
			'4': '16px',
			'5': '20px',
			'6': '24px',
			full: '100%',
		},

		// 圆角规范
		borderRadius: {
			'0': '16px',
			'1': '24px',
		},
	},

	shortcuts: {
		// 文本样式
		title: 'font-cn text-title font-medium',
		h1: 'font-cn text-h1 font-medium',
		h2: 'font-cn text-h2 font-medium',
		text: 'font-cn text-text font-regular',
		input: 'font-cn text-input font-regular',
		caption: 'font-cn text-caption font-regular',
		'caption-sm': 'font-cn text-caption-sm font-regular',

		// 数字样式
		'num-title': 'font-din text-title font-medium',
		'num-h1': 'font-din text-h1 font-medium',
		'num-h2': 'font-din text-h2 font-medium',
		'num-text': 'font-din text-text font-regular',
		'num-input': 'font-din text-input font-regular',
		'num-caption': 'font-din text-caption font-regular',

		// 英文样式
		'en-lg': 'font-din text-lg font-medium',
		'en-md': 'font-din text-md font-medium',
		'en-sm': 'font-din text-sm font-medium',

		// 文本颜色变体
		'text-white': 'text-white',
		'text-dark': 'text-dark',
		'text-grey': 'text-grey-03',
		'text-highlight': 'text-highlight',
		'text-error': 'text-error',
		'text-success': 'text-success',
		'text-primary-color': 'text-primary',

		// 按钮样式
		'btn-sm': 'h-24px px-8px text-caption font-medium',
		'btn-md': 'h-32px px-12px text-input font-medium',
		'btn-lg': 'h-48px px-16px text-text font-medium',

		// 按钮颜色变体
		'btn-primary': 'bg-primary text-dark',
		'btn-dark': 'bg-dark text-white',
		'btn-highlight': 'bg-highlight text-white',
		'btn-error': 'bg-error text-white',
		'btn-success': 'bg-success text-white',
	},
})
