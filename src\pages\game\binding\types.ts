/**
 * 游戏绑定相关类型定义
 * 专注于表单绑定功能
 */

// 绑定类型枚举
export enum BindingType {
  /** API绑定：通过表单字段绑定 */
  API = 'api',
  /** 短信验证绑定：手机号+验证码 */
  SMS = 'sms'
}

// 表单字段类型
export enum FieldType {
  /** 文本输入框 */
  TEXT = 'text',
  /** 选择器 */
  SELECT = 'select',
  /** 手机号输入框 */
  PHONE = 'phone',
  /** 验证码输入框 */
  VERIFICATION_CODE = 'verification_code'
}

// 选择器选项
export interface SelectOption {
  value: string
  label: string
}

// 表单字段配置
export interface FormFieldConfig {
  /** 字段标识符 */
  model: string
  /** 字段标签 */
  label: string
  /** 占位符文本 */
  placeholder: string
  /** 字段类型 */
  type: FieldType
  /** 是否必填 */
  required?: boolean
  /** 选择器选项（仅当type为SELECT时使用） */
  options?: SelectOption[]
  /** 选项API端点（动态获取选项时使用） */
  optionsApi?: string
  /** 字段验证规则 */
  validation?: {
    /** 最小长度 */
    minLength?: number
    /** 最大长度 */
    maxLength?: number
    /** 正则表达式验证 */
    pattern?: string
    /** 错误提示信息 */
    message?: string
  }
  /** 是否支持验证码按钮 */
  hasVerificationButton?: boolean
}

// API配置
export interface ApiConfig {
  /** 绑定API端点 */
  bindEndpoint: string
  /** 发送验证码API端点（仅SMS类型使用） */
  sendCodeEndpoint?: string
}

// 游戏绑定配置
export interface GameBindingConfig {
  /** 游戏ID */
  gameId: string
  /** 游戏名称 */
  gameName: string
  /** 游戏Logo URL */
  logoUrl: string
  /** 绑定类型 */
  bindingType: BindingType
  /** 表单字段配置 */
  formFields: FormFieldConfig[]
  /** API配置 */
  apiConfig: ApiConfig
  /** 绑定按钮文本 */
  buttonText?: string
}

// 绑定请求参数
export interface BindingParams {
  gameId: string
  [key: string]: any
}

// 绑定响应结果
export interface BindingResult {
  code: number
  message: string
  status: string
  data?: {
    id: number
    userId: number
    gameId: string
    roleId: string
    roleName: string
    bindChannel: string
    bindSource: string
    bindStatus: number
    roleIdShow?: string
    createTime?: string | null
    updateTime?: string | null
  }
}

// 验证码状态
export interface VerificationCodeState {
  /** 是否正在倒计时 */
  isCountingDown: boolean
  /** 剩余秒数 */
  countdown: number
  /** 计时器ID */
  timerId?: NodeJS.Timeout
}

// 服务器信息
export interface ServerInfo {
  id: string
  name: string
  region?: string
}
