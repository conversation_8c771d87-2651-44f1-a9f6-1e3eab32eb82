<script setup lang="ts">

const props = defineProps<{
  success: boolean
  message?: string
  errorMessage?: string
  buttonText?: string
}>()

const emit = defineEmits<{
  (e: 'confirm'): void
  (e: 'retry'): void
}>()

// 确认按钮点击事件
function handleConfirm() {
  emit('confirm')
}

// 重试按钮点击事件
function handleRetry() {
  emit('retry')
}
</script>

<template>
  <div class="bind-result-container h-full w-full flex items-center justify-center">
    <div class="result-content flex flex-col items-center justify-center p-8 w-full max-w-md mb-48">
      <var-icon 
        :name="success ? 'checkbox-marked-circle-outline' : 'alert-circle-outline'" 
        :class="success ? 'text-[#4CAF50]' : 'text-[#F44336]'"
        size="68"
      />
      
      <div class="text-xl font-medium mt-4 text-center">
        {{ success ? (message || '绑定成功') : (errorMessage || '绑定失败') }}
      </div>
      
      <p v-if="!success" class="text-[#666] text-sm mt-2 text-center">
        请稍后重试或联系客服解决
      </p>
      
      <var-button 
        class="mt-8 h-[48px] w-full rounded-2xl bg-[#FFD722] text-[#333] font-medium text-base border-none"
        :elevation="false"
        text
        @click="success ? handleConfirm() : handleRetry()"
      >
        {{ buttonText || (success ? '确定' : '重试') }}
      </var-button>
    </div>
  </div>
</template>

<style scoped>
.bind-result-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.result-content {
  background-color: white;
  border-radius: 16px;
}
</style> 