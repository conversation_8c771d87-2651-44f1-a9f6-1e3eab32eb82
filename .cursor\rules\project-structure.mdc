---
description: 项目的目录结构和命名规范。
globs: 
alwaysApply: false
---
# 项目结构与命名规范

本规则 **强制** 定义项目的文件、目录组织方式以及代码元素的命名约定。
**MUST**: 严格遵循以下规范。

## 目录结构 (src/)

*   **MUST**: 遵循以下核心目录结构：
    ```plaintext
    src/
    ├── api/                 # API 定义 (按模块划分)
    ├── assets/              # 静态资源 (图片, 字体等)
    ├── components/          # 全局通用 UI 组件
    ├── composables/         # 全局可复用组合式函数 (逻辑)
    ├── constants/           # 全局常量
    ├── layouts/             # 页面布局组件
    ├── middleware/          # 路由中间件
    ├── pages/               # 页面视图组件 (自动路由)
    ├── plugins/             # Vue 插件
    ├── services/            # 业务服务层 (可选, 复杂业务逻辑封装)
    ├── stores/              # Pinia 状态管理模块
    ├── styles/              # 全局样式 (基础样式, 主题等)
    ├── types/               # 全局 TypeScript 类型定义
    └── utils/               # 通用工具函数
    ```
*   **页面 (`src/pages/`) 内部结构**:
    *   **SHOULD**: 对于功能复杂的页面，采用模块化组织：
        ```plaintext
        pages/
        └── feature-name/
            ├── index.vue       # 页面主入口
            ├── api.ts          # 该页面专属的 API 请求
            ├── components/     # 该页面专属的子组件
            ├── composables/    # 该页面专属的组合式函数
            └── types.ts        # 该页面专属的类型定义
        ```
    *   **INFO**: 简单的页面可以直接使用 `feature-name.vue` 文件。
*   **组件 (`src/components/`) 结构**:
    *   **MUST**: 全局组件使用文件夹形式，包含 `index.vue` 和可能的关联文件。
        ```plaintext
        components/
        └── MyComponent/
            ├── index.vue
            ├── styles.css  (或 <style> 块)
            └── types.ts    (如果需要)
        ```
    *   **AVOID**: 直接在 `components/` 下创建单文件组件 (`MyComponent.vue`)，除非是非常简单的纯展示组件。

## 命名规范

*   **目录名**: `kebab-case` (小写连字符)。
    *   `MUST`: `user-profile`, `game-list`, `recharge-history`
*   **文件名**: 
    *   Vue 组件: `PascalCase.vue`。 **MUST**: `UserProfileCard.vue`, `GameItem.vue`。
    *   TypeScript 文件 (.ts): `kebab-case.ts` 或 `camelCase.ts` (团队内部保持一致，推荐 `kebab-case`)。
        *   **SHOULD**: `user-api.ts`, `game-store.ts`, `use-counter.ts`
    *   样式文件 (.css, .scss): `kebab-case.css`。
*   **变量名**: `camelCase` (小驼峰)。
    *   **MUST**: 具有描述性。`isLoading`, `currentUser`, `gameListData`。
    *   **SHOULD**: 对于 `ref` 包裹的变量，可以使用 `xxxRef` 后缀，但非强制。 `countRef = ref(0)`。
*   **常量名**: `UPPER_SNAKE_CASE` (大写下划线)。
    *   **MUST**: `MAX_LOGIN_ATTEMPTS`, `DEFAULT_AVATAR_URL`。
    *   **MUST**: 使用 `as const` 定义的常量对象，其属性名仍遵循 `UPPER_SNAKE_CASE`。
*   **函数/方法名**: `camelCase` (小驼峰)。
    *   **MUST**: 通常使用 动词/动词+名词 形式。`fetchUserData`, `submitForm`, `isValid`。
    *   **Composables**: 使用 `use` 前缀。 **MUST**: `useCounter`, `useScrollPosition`。
*   **类型/接口名**: `PascalCase` (大驼峰)。
    *   **MUST**: `UserInfo`, `ApiResponse`, `GameSettings`。
*   **CSS 类名 (UnoCSS)**: **MUST** 使用 UnoCSS 原子化类名，**AVOID** 自定义非原子化类名，除非在特殊情况下（如覆盖第三方库样式）。查阅 [样式规则 (styling.mdc)](mdc:styling.mdc)。