<script setup lang="ts">
import { computed } from 'vue'
import { RewardItem, RewardStatus } from '../api'

interface Props {
  reward: RewardItem
  status: RewardStatus
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'claim', rewardId: string): void
}>()

// 按钮文本
const buttonText = computed(() => {
  switch (props.status) {
    case -1:
      return '未参与'
    case 0:
      return '领取'
    case 1:
      return '已领取'
    default:
      return '领取'
  }
})

// 按钮禁用状态
const isButtonDisabled = computed(() => {
  return props.status === 1
})

// 按钮类型
const buttonType = computed(() => {
  return props.status === 1 ? '' : '#FFD722'
})

// 按钮点击
const handleClaim = () => {
  emit('claim', props.reward.id)
}

// 将奖励详情分组，每行最多显示两个
const detailGroups = computed(() => {
  const details = props.reward.details || []
  const groups = []
  
  for (let i = 0; i < details.length; i += 2) {
    groups.push(details.slice(i, i + 2))
  }
  
  return groups
})
</script>

<template>
  <div class="reward-item flex items-center rounded-lg p-3 px-4 border border-[#3D4C6C]">
    <!-- 左侧图片 -->
    <div class="w-12 h-12 flex-shrink-0 rounded overflow-hidden mr-3 bg-[#1E2B4B] flex items-center justify-center">
      <img :src="reward.mainImage" :alt="reward.name" class="w-9 h-9 object-cover" />
    </div>
    
    <!-- 中间详情 -->
    <div class="flex-1 min-w-0">
      <h3 class="h2 font-medium m-0 mb-1.5 text-transparent bg-gradient-to-b from-[#719AC9] to-[#FFFFFF] bg-clip-text ">{{ reward.name }}</h3>
      <p v-if="reward.description" class="text-xs text-[rgba(255,255,255,0.7)]  mb-1.5">{{ reward.description }}</p>
      
      <!-- 奖励详情 -->
      <div>
        <div 
          v-for="(group, groupIndex) in detailGroups" 
          :key="groupIndex"
          class="flex gap-3 mb-1 last:mb-0"
        >
          <div 
            v-for="(detail, detailIndex) in group" 
            :key="`${groupIndex}-${detailIndex}`"
            class="flex items-center"
          >
            <img :src="detail.image" :alt="detail.description" class="w-4 h-4 mr-1.5" />
            <div class="flex">
              <span class="text-md text-primary-half">{{ detail.description }}</span>
              <span class="text-md font-medium text-primary-half">*{{ detail.count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 右侧按钮 -->
    <div class="ml-3">
      <var-button
        :color="buttonType"
        :disabled="isButtonDisabled"
        class="h-6 text-md rounded-full px-3 text-dark"
        @click="handleClaim"
      >
        {{ buttonText }}
      </var-button>
    </div>
  </div>
</template> 

<style scoped>
.reward-item {
  background: linear-gradient(90deg, 
    rgba(58, 74, 114, 0.4) 0%,
    rgba(58, 74, 114, 0.7) 4.9%,
    rgba(58, 74, 114, 0.7) 47.5%,
    rgba(58, 74, 114, 0.7) 96%,
    rgba(58, 74, 114, 0.4) 100%
  );
}
</style>


