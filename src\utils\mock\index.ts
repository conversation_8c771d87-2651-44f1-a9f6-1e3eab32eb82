import { http, createApi } from '../http'
import type { RequestConfig, ApiResponse } from '../http/types'

/**
 * 创建模拟API响应
 * @param data 要返回的数据
 * @param delay 延迟毫秒数
 * @returns Promise包装的响应对象
 */
export function mockResponse<T>(data: T, delay = 300): Promise<ApiResponse<T>> {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve({
        data,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {} as RequestConfig
      })
    }, delay)
  })
}

/**
 * 创建模拟API
 * @param apiObject API对象
 * @returns 模拟的API对象
 */
export function createMockApi<T extends Record<string, Function>>(apiObject: T): T {
  const mockApiObject = {} as T
  
  for (const key in apiObject) {
    mockApiObject[key] = ((...args: any[]) => {
      // 这里我们可以查看原始函数的实现，来决定返回什么模拟数据
      const originalFn = apiObject[key]
      // 调用者可以传入模拟数据作为最后一个参数
      const lastArg = args[args.length - 1]
      const mockData = lastArg?.__mockData
      const mockDelay = lastArg?.__mockDelay ?? 300
      
      if (mockData !== undefined) {
        args.pop() // 移除包含模拟数据的参数
        return mockResponse(mockData, mockDelay)
      }
      
      // 默认情况下，我们简单地返回一个空对象
      console.warn(`Mock API called for ${key} without mock data`)
      return mockResponse({}, mockDelay)
    }) as any
  }
  
  return mockApiObject
}

/**
 * 为API添加模拟数据
 * @param fn API函数
 * @param mockData 模拟数据
 * @param delay 延迟毫秒数
 * @returns 带有模拟数据的API函数参数
 */
export function withMockData<T>(mockData: T, delay = 300) {
  return { __mockData: mockData, __mockDelay: delay }
} 