import { ref, computed } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { ticketApi } from '@/api'
import type { CreateTicketParams } from '@/api/contact/types'
import { useUserStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'

/**
 * 创建工单相关Hook
 */
export function useTicketCreate() {
  const userStore = useUserStore()
  const toast = useToast()
  
  // 表单数据
  const formData = ref<CreateTicketParams>({
    title: '',
    content: '',
    gameId: userStore.activeGameId ? String(userStore.activeGameId) : undefined
  })
  
  // 提交工单
  const {
    data: createTicketData,
    loading: createLoading,
    run: createTicket
  } = useApiRequest(
    (params: CreateTicketParams) => ticketApi.createTicket(params),
    {
      manual: true,
      onSuccess: (response) => {
        if (response.data) {
          toast.success('工单提交成功，我们将尽快处理')
          // 重置表单
          formData.value = {
            title: '',
            content: '',
            gameId: userStore.activeGameId ? String(userStore.activeGameId) : undefined
          }
        }
      },
      onError: () => {
        toast.error('提交失败，请稍后再试')
      }
    }
  )
  
  // 更新表单字段
  const updateField = (field: keyof CreateTicketParams, value: string) => {
    formData.value = {
      ...formData.value,
      [field]: value
    }
  }
  
  // 提交表单
  const submitForm = async () => {
    // 表单验证
    if (!formData.value.title.trim()) {
      toast.warning('请输入工单标题')
      return false
    }
    
    if (!formData.value.content.trim()) {
      toast.warning('请输入工单内容')
      return false
    }
    
    try {
      const result = await createTicket(formData.value)
      return result
    } catch (error) {
      console.error('提交工单失败', error)
      return null
    }
  }
  
  return {
    formData,
    loading: createLoading,
    updateField,
    submitForm,
    createdTicket: computed(() => createTicketData.value?.data)
  }
} 