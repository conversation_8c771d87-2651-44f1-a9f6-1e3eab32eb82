# 首页功能说明

## 功能概述

首页是用户进入应用后的主要界面，展示用户信息、游戏角色、广告轮播和服务入口。

## 核心功能

### 1. 数据初始化
- 页面加载时自动获取用户绑定信息、游戏列表、主题配置
- 根据当前激活角色获取对应游戏信息和主题配置
- 支持开发环境模拟数据和生产环境真实API

### 2. 页面布局
- **顶部区域**: 游戏切换按钮 + 用户信息卡片
- **角色信息**: 当前激活角色的详细信息展示
- **广告轮播**: 自动播放的广告图片轮播
- **服务卡片**: 充值优惠和客服服务入口

### 3. 组件架构

#### Composables
- `useHomeData`: 页面数据管理，负责初始化、获取、刷新数据
- `useBannerCarousel`: 广告轮播逻辑，处理自动播放和点击跳转

#### 组件
- `GameSwitchButton`: 游戏切换按钮
- `UserInfoCard`: 用户信息展示
- `RoleInfoCard`: 角色信息卡片
- `BannerCarousel`: 广告轮播组件
- `ServiceCard`: 服务卡片组件

## 技术实现

### 状态管理
- 使用 Pinia 管理游戏、用户、主题状态
- 遵循项目API字段命名规范，不进行字段转换

### 样式设计
- 使用 UnoCSS 原子化样式，所有组件都采用原子化类名
- 响应式设计，适配不同屏幕尺寸
- 遵循 Figma 设计稿的视觉效果
- 移除了所有 scoped style，统一使用 UnoCSS 类名

### 数据流
1. 页面挂载 → 检查绑定信息
2. 获取游戏数据和主题配置
3. 根据当前激活角色渲染对应内容
4. 用户交互 → 更新状态 → 重新渲染

## 待实现功能

- [ ] 游戏切换的左侧tab展开
- [ ] 角色管理弹框
- [ ] 具体的服务页面跳转逻辑
- [ ] 真实API接口对接

## 开发说明

### 模拟数据
开发环境使用模拟数据，包含：
- 游戏列表（超自然行动组、PVP模式）
- 主题配置（背景、轮播图、服务）
- 用户信息（头像、昵称）

### 环境配置
- 开发环境：使用模拟数据，1秒延迟
- 生产环境：使用真实API接口

### UnoCSS 使用
所有组件都使用 UnoCSS 原子化样式：
- 布局：`flex`, `grid`, `relative`, `absolute` 等
- 间距：`p-4`, `m-2`, `gap-3` 等
- 颜色：`bg-[#1C1B1A]`, `text-white` 等
- 尺寸：`w-full`, `h-16`, `text-sm` 等
- 效果：`rounded-xl`, `shadow-lg`, `transition-all` 等

### 调试信息
控制台会输出详细的数据加载和状态更新日志，便于调试。