import { ref, Ref, computed } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { rewardsApi } from '@/api'
import { useUserStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'

/**
 * 获取奖励状态
 * @returns 奖励相关功能
 */
export function useRewardStatus() {
  const userStore = useUserStore()
  const toast = useToast()
  const gameId = computed(() => userStore.activeGameId)
  
  // 获取可用奖励列表
  const {
    data: availableRewardsData,
    loading: availableRewardsLoading,
    error: availableRewardsError,
    run: fetchAvailableRewards
  } = useApiRequest(
    (params) => rewardsApi.getAvailableRewards(params),
    {
      manual: true,
      refreshDeps: [gameId]
    }
  )
  
  // 获取已领取奖励列表
  const {
    data: receivedRewardsData,
    loading: receivedRewardsLoading,
    run: fetchReceivedRewards
  } = useApiRequest(
    (params) => rewardsApi.getReceivedRewards(params),
    {
      manual: true
    }
  )
  
  // 可用奖励列表
  const availableRewards = computed(() => {
    return availableRewardsData.value?.data?.list || []
  })
  
  // 已领取奖励列表
  const receivedRewards = computed(() => {
    return receivedRewardsData.value?.data?.list || []
  })
  
  // 是否有任一加载中
  const isLoading = computed(() => {
    return availableRewardsLoading.value || receivedRewardsLoading.value
  })
  
  // 领取奖励API
  const {
    loading: claimLoading,
    run: claimRewardApi
  } = useApiRequest(
    (packageId: string) => rewardsApi.claimReward(packageId),
    {
      manual: true,
      onSuccess: () => {
        toast.success('奖励领取成功')
        // 重新加载列表
        fetchAvailableRewards()
      },
      onError: () => {
        toast.error('领取失败，请稍后再试')
      }
    }
  )
  
  // 领取奖励处理函数
  const claimReward = async (packageId: string) => {
    try {
      await claimRewardApi(packageId)
      return true
    } catch (error) {
      console.error('领取奖励失败', error)
      return false
    }
  }
  
  // 刷新奖励状态
  const refreshStatus = () => {
    if (availableRewards.value.length > 0) {
      fetchAvailableRewards()
    }
  }

  return {
    // 数据
    availableRewards,
    receivedRewards,
    
    // 加载状态
    isLoading,
    availableRewardsLoading,
    receivedRewardsLoading,
    
    // 方法
    fetchAvailableRewards,
    fetchReceivedRewards,
    claimReward,
    refreshStatus
  }
} 