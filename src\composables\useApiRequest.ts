import { useRequest, UseRequestOptions, UseRequestReturn } from 'vue-request'
import { reactive, ref, unref } from 'vue'
import type { MaybeRef } from '@vueuse/core'
import { mockResponse } from '../utils/mock'

/**
 * 封装vue-request，添加了自动处理模拟数据的支持
 *
 * @template TData 请求返回的数据类型
 * @template TParams 请求参数类型
 * @param service 请求函数或API方法
 * @param options vue-request选项
 * @param mockData 可选的模拟数据，开发环境下会优先使用
 * @returns UseRequestReturn
 */
export function useApiRequest<TData, TParams extends any[]>(
  service: (...args: TParams) => Promise<TData>,
  options?: UseRequestOptions<TData, TParams>,
  mockData?: MaybeRef<TData | undefined>
): UseRequestReturn<TData, TParams> {
  // 是否为开发环境且提供了模拟数据
  const useMock = import.meta.env.DEV && mockData !== undefined
  
  // 如果使用模拟数据，则替换服务函数
  const actualService = useMock 
    ? (...args: TParams) => {
        const data = unref(mockData)
        // 模拟网络延迟
        return mockResponse(data as TData).then(res => res.data)
      }
    : service
    
  // 调用原始的useRequest
  return useRequest(actualService, options)
}

/**
 * 创建带有加载状态的API方法包装
 * 
 * @param apiMethods API方法对象
 * @returns 包装的API方法，以及加载状态
 */
export function createApiWithLoading<T extends Record<string, Function>>(apiMethods: T) {
  const loadingState = reactive<Record<string, boolean>>({})
  
  const wrappedApi = {} as T & { loading: Record<string, boolean> }
  
  for (const key in apiMethods) {
    const originalMethod = apiMethods[key] as Function
    loadingState[key] = false
    
    wrappedApi[key] = async (...args: any[]) => {
      loadingState[key] = true
      try {
        return await originalMethod(...args)
      } finally {
        loadingState[key] = false
      }
    }
  }
  
  wrappedApi.loading = loadingState
  
  return wrappedApi
} 