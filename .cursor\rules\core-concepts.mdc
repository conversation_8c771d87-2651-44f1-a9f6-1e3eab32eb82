---
description: Vue3、Vite 和 TypeScript 的核心概念与基础规范。
globs: 
alwaysApply: false
---
# Vue 3, Vite, TypeScript 核心规范

本规则明确项目核心技术栈（Vue 3, Vite, TypeScript）的基础使用规范和约定。

## Vue 3

*   **MUST**: 使用 Composition API 配合 `<script setup>` 语法糖。
    ```vue
    <script setup lang="ts">
    import { ref, computed, onMounted } from 'vue'

    const count = ref(0)
    const double = computed(() => count.value * 2)

    onMounted(() => {
      console.log('Component mounted')
    })
    </script>
    ```
*   **MUST**: 正确理解和使用响应式系统 API (`ref`, `reactive`, `computed`, `watch`, `watchEffect`)。
    *   **AVOID**: 直接修改 `props`。
    *   **SHOULD**: 对复杂对象或数组使用 `reactive`，对基本类型或需要 `.value` 访问的单个值使用 `ref`。
*   **MUST**: 使用 `defineProps` 和 `defineEmits` 定义组件接口。
    *   **SHOULD**: 为 `props` 提供明确的类型定义，并使用 `withDefaults` 设置默认值。
    ```vue
    <script setup lang="ts">
    interface Props {
      message: string;
      count?: number;
    }
    const props = withDefaults(defineProps<Props>(), {
      count: 0
    })

    const emit = defineEmits<{ (e: 'update', value: number): void }>()

    function increment() {
      emit('update', (props.count || 0) + 1)
    }
    </script>
    ```
*   **MUST**: 使用 Vue 生命周期钩子（`onMounted`, `onUnmounted` 等）处理副作用。
*   **AVOID**: 直接操作 DOM，优先使用模板引用 (`ref`)。

## Vite

*   **MUST**: 理解并利用基于文件系统的路由 (`src/pages` 目录)。查阅 [路由规则 (routing.mdc)](mdc:routing.mdc)。
*   **INFO**: Vite 默认启用路由懒加载，无需手动 `import()`。
*   **SHOULD**: 了解 `vite.config.ts` 中的关键配置，如 `plugins`, `resolve.alias`, `server`, `build` 等，但 **AVOID** 随意修改。
*   **MUST**: 使用环境变量 (`.env`, `.env.development`, `.env.production`) 管理不同环境的配置。通过 `import.meta.env` 访问。

## TypeScript

*   **MUST**: 为所有变量、函数参数、返回值添加明确的类型注解。
*   **MUST**: 使用 `interface` 定义对象的结构（如 API 响应、复杂 Props）。
    ```typescript
    // MUST
    interface UserProfile {
      userId: string;
      nickname: string;
      avatarUrl?: string;
    }
    ```
*   **MUST**: 使用 `type` 定义联合类型、交叉类型或函数的类型签名。
    ```typescript
    // MUST
    type Status = 'pending' | 'success' | 'error';
    type UserResponse = UserProfile & { token: string };
    type FetchDataFunction = (id: string) => Promise<UserProfile>;
    ```
*   **MUST**: 使用 `as const` 创建类型安全的常量映射，替代 `enum`。
    ```typescript
    // MUST
    const OrderStatus = {
      PENDING: 1,
      PROCESSING: 2,
      COMPLETED: 3,
      CANCELLED: 4
    } as const;
    type OrderStatus = typeof OrderStatus[keyof typeof OrderStatus]; // 值为 1 | 2 | 3 | 4

    // AVOID: enum OrderStatus { PENDING, PROCESSING, ... }
    ```
*   **SHOULD**: 利用 TypeScript 泛型 (`<T>`) 提高代码的复用性和类型安全性。
*   **SHOULD**: 优先使用 ES Modules (`import`/`export`) 语法。
*   **AVOID**: 使用 `any` 类型，除非绝对必要且有充分理由。优先考虑 `unknown` 或更具体的类型。
*   **MUST**: 遵循 `tsconfig.json` 中定义的编译选项。