import { defineStore } from 'pinia'
import { gameApi } from '@/api'
import type { BindingItem } from '@/api/game/types'

// 游戏信息 - 与API返回数据结构保持一致
export interface Game {
  gameId: number
  name: string
  iconLogo: string // 短图
  wideLogo: string // 长图
  avatar: string
}

// 游戏账户信息 - 与API返回数据结构保持一致
export interface GameAccount {
  accountId: string
  uid: string
  name: string
  avatar: string
  gameId: number
  gameName: string
  isActive: boolean
  bindingId: number
}

// 用户绑定的角色信息 - 与API返回数据结构保持一致
export interface UserBinding {
  bindingId: number
  gameId: number
  roleName: string
  roleIdShow: string
  avatar: string
  isActive: boolean
}

export const useGameStore = defineStore('game', {
  state: () => ({
    // 游戏列表
    games: [] as Game[],
    // 用户绑定的角色列表
    userGameList: [] as UserBinding[],
    // 当前激活的角色
    activeRole: null as UserBinding | null,
  }),

  getters: {
    // 当前激活的游戏
    activeGameId(): number | null {
      if (!this.activeRole) return null
      return this.activeRole!.gameId
    },

    // 根据游戏ID获取游戏信息
    getGameById: (state) => (gameId: number) => {
      return state.games.find(game => game.gameId === gameId) || null
    },

    // 获取某游戏下的所有角色
    getUserGameListByGameId: (state) => (gameId: number) => {
      return state.userGameList.filter((bindList) => bindList.gameId === gameId)
    },

    // 已绑定的游戏列表（去重）
    boundGames(): Game[] {
      const boundGameIds = new Set(this.userGameList.map(b => b.gameId))
      return this.games.filter(game => boundGameIds.has(game.gameId))
    },

    // 是否有绑定的角色
    hasuserGameList(): boolean {
      return this.userGameList.length > 0
    }
  },

  actions: {
    /**
     * 加载用户绑定信息（统一入口）
     */
    async loadUserGameList() {
      try {
        const response = await gameApi.getBindList()
        if (response.data && Array.isArray(response.data)) {
          this.setuserGameList(response.data)
        }
        console.log('[GameStore] 用户绑定信息加载完成')
      } catch (error) {
        console.error('[GameStore] 加载用户绑定信息失败:', error)
        throw error
      }
    },

    /**
     * 设置游戏列表
     */
    setGames(games: Game[]) {
      this.games = games
      console.log(`[GameStore] 设置游戏列表: ${games.length} 个游戏`)
    },

    /**
     * 设置用户绑定列表
     */
    setuserGameList(userGameList: UserBinding[]) {
      this.userGameList = userGameList
      console.log(`[GameStore] 设置绑定列表: ${userGameList.length} 个角色`)


      // 设置当前激活角色
      const activeRole = userGameList.find((b) => b.isActive)
      if (activeRole) {
        this.activeRole = activeRole
        const game = this.getGameById(activeRole.gameId)
        console.log(`[GameStore] 激活角色: ${activeRole.roleName} (游戏: ${game?.name || activeRole.gameId})`)
      } else if (userGameList.length > 0) {
        // 如果没有激活角色，选择第一个
        this.setActiveRole(userGameList[0])
      }
    },

    /**
     * 设置激活角色
     */
    async setActiveRole(binding: UserBinding) {
      try {
        // 调用API切换激活角色
        await gameApi.switchActiveGame({ bindingId: binding.bindingId })

        // 更新本地状态
        this.userGameList.forEach(b => b.isActive = false)
        binding.isActive = true
        this.activeRole = binding

        console.log(`[GameStore] 切换激活角色: ${binding.roleName} (游戏ID: ${binding.gameId})`)
      } catch (error) {
        console.error('[GameStore] 切换角色失败:', error)
        throw error
      }
    },
    /**
     * 添加新绑定
     */
    addBinding(binding: UserBinding) {
      // 检查是否已存在
      const existingIndex = this.userGameList.findIndex(b => b.bindingId === binding.bindingId)

      if (existingIndex >= 0) {
        this.userGameList[existingIndex] = binding
      } else {
        this.userGameList.push(binding)
      }

      console.log(`[GameStore] 添加绑定: ${binding.roleName} (游戏ID: ${binding.gameId})`)
    },

    /**
     * 解绑角色
     */
    async removeBinding(bindingId: number) {
      try {
        // 调用API解绑
        await gameApi.unbindGame({ bindingId })

        // 重新获取绑定列表
        await this.refreshUserGameList()

        console.log(`[GameStore] 解绑成功: ${bindingId}`)
      } catch (error) {
        console.error('[GameStore] 解绑失败:', error)
        throw error
      }
    },

    /**
     * 刷新绑定列表
     */
    async refreshUserGameList() {
      try {
        await this.loadUserGameList()
        console.log('[GameStore] 绑定列表刷新完成')
      } catch (error) {
        console.error('[GameStore] 刷新绑定列表失败:', error)
        throw error
      }
    },

    /**
     * 清除所有数据
     */
    clear() {
      this.games = []
      this.userGameList = []
      this.activeRole = null
      console.log('[GameStore] 清除所有数据')
    }
  },

  persist: {
    enabled: true,
    storage: localStorage,
    key: '__giant__game__store__',
  },
}) 