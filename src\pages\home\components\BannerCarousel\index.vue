<template>
  <div class="w-full h-[170px] rounded-xl overflow-hidden shadow-[0px_1px_4px_0px_rgba(0,0,0,0.15)] relative" v-if="hasBanners">
    <var-swipe 
      :autoplay="4000" 
      :show-indicators="banners.length > 1"
      :loop="true"
      class="w-full h-full"
      @change="handleSwipeChange"
    >
      <var-swipe-item 
        v-for="(banner, index) in banners" 
        :key="banner.id"
        class="w-full h-full relative cursor-pointer overflow-hidden"
        @click="handleBannerClick(banner)"
      >
        <img 
          :src="banner.imageUrl" 
          :alt="banner.title || `Banner ${index + 1}`"
          class="w-full h-full object-cover"
        />
        <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-[rgba(55,3,105,0.8)] to-[rgba(25,3,45,0.6)] flex items-center justify-center">
          <div class="text-center text-white">
            <h3 v-if="banner.title" class="text-lg font-semibold mb-2 text-shadow-[0px_2px_4px_rgba(0,0,0,0.3)]">{{ banner.title }}</h3>
            <div class="text-sm opacity-90 text-shadow-[0px_1px_2px_rgba(0,0,0,0.3)]">复利合集</div>
          </div>
        </div>
      </var-swipe-item>
    </var-swipe>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useBannerCarousel, type BannerItem } from '../../composables/useBannerCarousel'

interface Props {
  banners: BannerItem[]
}

const props = defineProps<Props>()

const {
  hasBanners,
  handleBannerClick
} = useBannerCarousel(props.banners)

// 处理Swipe切换事件
const handleSwipeChange = (index: number) => {
  // 可以在这里添加切换时的额外逻辑
  console.log('轮播切换到:', index)
}
</script>