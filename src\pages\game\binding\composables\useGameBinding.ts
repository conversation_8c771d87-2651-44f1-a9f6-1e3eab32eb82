/**
 * 游戏绑定组合式函数
 * 使用项目现有的 utils.http 进行请求
 */

import { ref, reactive, computed, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useToast } from '@/composables/useToast'
import { request } from '@/utils/http'
import { 
  GameBindingConfig, 
  BindingResult, 
  BindingParams,
  FieldType,
  BindingType,
  VerificationCodeState,
  SelectOption
} from '../types'
import { getGameBindingConfig } from '../config'

export function useGameBinding() {
  const route = useRoute()
  const toast = useToast()

  // 状态管理
  const loading = ref(false)
  const config = ref<GameBindingConfig | null>(null)
  const formData = reactive<Record<string, any>>({})
  const bindingResult = ref<BindingResult | null>(null)
  
  // 验证码状态
  const verificationState = ref<VerificationCodeState>({
    isCountingDown: false,
    countdown: 60
  })

  // 动态选项数据
  const dynamicOptions = reactive<Record<string, SelectOption[]>>({})

  // 获取游戏ID
  const gameId = computed(() => {
    return route.params.gameId as string || route.query.gameId as string
  })

  // 获取游戏配置
  const loadGameConfig = async () => {
    if (!gameId.value) {
      toast.error('游戏ID不能为空')
      return false
    }

    const gameConfig = getGameBindingConfig(gameId.value)
    if (!gameConfig) {
      toast.error(`不支持的游戏: ${gameId.value}`)
      return false
    }

    config.value = gameConfig

    // 初始化表单数据
    initFormData()

    // 加载动态选项
    await loadDynamicOptions()

    return true
  }

  // 初始化表单数据
  const initFormData = () => {
    if (!config.value) return

    config.value.formFields.forEach(field => {
      formData[field.model] = ''
    })
  }

  // 加载动态选项（如服务器列表）
  const loadDynamicOptions = async () => {
    if (!config.value) return

    const dynamicFields = config.value.formFields.filter(
      field => field.type === FieldType.SELECT && field.optionsApi
    )

    // 如果没有需要动态加载的字段，直接返回
    if (dynamicFields.length === 0) {
      return
    }

    // 并行加载所有动态选项
    const loadPromises = dynamicFields.map(async (field) => {
      try {
        const response = await request<{ code: number; message: string; data: SelectOption[] }>({
          url: field.optionsApi!,
          method: 'GET'
        })
        dynamicOptions[field.model] = response.data.data || []
      } catch (error) {
        // 设置空数组作为fallback
        dynamicOptions[field.model] = []
      }
    })

    await Promise.all(loadPromises)
  }

  // 验证表单数据
  const validateForm = async () => {
    if (!config.value) return false

    for (const field of config.value.formFields) {
      const value = formData[field.model]
      
      // 检查必填字段
      if (field.required && !value) {
        toast.error(`请填写${field.label}`)
        return false
      }

      // 执行字段验证
      if (value && field.validation) {
        const validation = field.validation

        // 长度验证
        if (validation.minLength && value.length < validation.minLength) {
          toast.error(validation.message || `${field.label}长度不能少于${validation.minLength}位`)
          return false
        }
        if (validation.maxLength && value.length > validation.maxLength) {
          toast.error(validation.message || `${field.label}长度不能超过${validation.maxLength}位`)
          return false
        }

        // 正则验证
        if (validation.pattern && !new RegExp(validation.pattern).test(value)) {
          toast.error(validation.message || `${field.label}格式不正确`)
          return false
        }
      }
    }

    return true
  }

  // 执行绑定
  const performBinding = async () => {
    if (!config.value || loading.value) return null

    // 验证表单
    const isValid = await validateForm()
    if (!isValid) return null

    loading.value = true
    bindingResult.value = null

    try {
      // 准备绑定数据
      const bindData: BindingParams = {
        gameId: gameId.value,
        ...formData
      }

      // 调用绑定API
      const response = await request<BindingResult>({
        url: config.value.apiConfig.bindEndpoint,
        method: 'POST',
        data: bindData
      })

      bindingResult.value = response
      return response

    } catch (error: any) {
      const errorMessage = error.message || '绑定失败，请稍后重试'
      
      const result: BindingResult = {
        code: -1,
        message: errorMessage,
        status: 'error'
      }
      bindingResult.value = result
      return result
    } finally {
      loading.value = false
    }
  }

  // 发送验证码
  const sendVerificationCode = async () => {
    if (!config.value || !config.value.apiConfig.sendCodeEndpoint) return

    // 检查是否正在倒计时
    if (verificationState.value.isCountingDown) {
      toast.error(`请等待${verificationState.value.countdown}秒后重试`)
      return
    }

    // 验证手机号
    const phoneNumber = formData.phoneNumber
    if (!phoneNumber) {
      toast.error('请先输入手机号')
      return
    }

    if (!/^1[3-9]\d{9}$/.test(phoneNumber)) {
      toast.error('请输入正确的手机号码')
      return
    }

    try {
      // 发送验证码
      const response = await request<{ code: number; message: string; status: string }>({
        url: config.value.apiConfig.sendCodeEndpoint,
        method: 'POST',
        data: {
          phoneNumber,
          gameId: gameId.value
        }
      })

      if (response.data.code === 0) {
        toast.success('验证码已发送')
        startCountdown()
      } else {
        toast.error(response.data.message || '发送验证码失败')
      }
    } catch (error: any) {
      console.error('发送验证码失败:', error)
      toast.error(error.message || '发送验证码失败')
    }
  }

  // 开始倒计时
  const startCountdown = () => {
    verificationState.value.isCountingDown = true
    verificationState.value.countdown = 60

    verificationState.value.timerId = setInterval(() => {
      verificationState.value.countdown--
      
      if (verificationState.value.countdown <= 0) {
        stopCountdown()
      }
    }, 1000)
  }

  // 停止倒计时
  const stopCountdown = () => {
    if (verificationState.value.timerId) {
      clearInterval(verificationState.value.timerId)
      verificationState.value.timerId = undefined
    }
    verificationState.value.isCountingDown = false
    verificationState.value.countdown = 60
  }

  // 清空表单
  const clearForm = () => {
    Object.keys(formData).forEach(key => {
      formData[key] = ''
    })
  }

  // 组件卸载时清理资源
  onUnmounted(() => {
    stopCountdown()
  })

  return {
    // 状态
    loading,
    config,
    formData,
    bindingResult,
    verificationState,
    dynamicOptions,
    gameId,

    // 方法
    loadGameConfig,
    performBinding,
    sendVerificationCode,
    clearForm,
    validateForm
  }
}
