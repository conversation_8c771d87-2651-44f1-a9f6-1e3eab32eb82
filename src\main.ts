// https://unocss.dev/ 原子 css 库
import '@unocss/reset/tailwind-compat.css' // unocss reset
import '@varlet/ui/es/style'
import 'virtual:uno.css'
import 'virtual:unocss-devtools'
import Varlet from '@varlet/ui'
import { createApp } from 'vue'
import App from './App.vue'
import directives from './directives'
// 你自定义的 css
import './styles/main.css'
import { initVersionCheck } from './utils/versionCheck'

const app = createApp(App)

// 使用Varlet UI组件库
app.use(Varlet)

// 注册全局自定义指令
app.use(directives)

// 插件目录下的文件会自动引入并注册

app.mount('#app')

initVersionCheck()
