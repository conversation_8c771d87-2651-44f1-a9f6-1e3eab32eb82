# Sentry 错误监控集成

基于 `@sentry/vue` 和 `@sentry/tracing` 封装的错误监控工具，支持全局异常捕获、HTTP请求错误监控及性能追踪。

## 基本使用

```typescript
import { createApp } from 'vue'
import router from './router'
import SentryPlugin from '@/utils/sentry'
import App from './App.vue'

const app = createApp(App)

// 初始化Sentry（建议在生产环境使用）
if (import.meta.env.PROD) {
  app.use(SentryPlugin, {
    dsn: 'https://<EMAIL>/project',
    environment: 'production',
    router, // 提供router实现路由追踪
    tracesSampleRate: 0.5 // 采样率，1.0表示100%上报
  })
}

app.mount('#app')
```

## HTTP错误监控

Sentry模块可以自动监控并上报HTTP请求中发生的错误：

```typescript
import { createApp } from 'vue'
import SentryPlugin from '@/utils/sentry'
import App from './App.vue'

const app = createApp(App)

app.use(SentryPlugin, {
  dsn: 'https://<EMAIL>/project',
  enableHttpMonitoring: true, // 默认启用
  httpMonitoringOptions: {
    // 忽略特定URL
    ignoreUrls: [/\/logs$/, /\/analytics$/],
    
    // 忽略特定HTTP状态码
    ignoreStatuses: [404, 429],
    
    // 忽略特定业务错误码
    ignoreBusinessCodes: [1001, 1002],
    
    // 设置采样率
    sampleRate: 0.5,  // 只上报50%的错误
    
    // 添加自定义标签
    tags: {
      app: 'wechat-web',
      platform: 'web'
    },
    
    // 是否在控制台也打印错误
    logToConsole: true
  }
})

app.mount('#app')
```

## 手动上报错误

可以在组件中使用Sentry手动上报错误：

```typescript
<script setup>
import { inject } from 'vue'

// 通过依赖注入获取Sentry
const Sentry = inject('sentry')

function handleDangerousOperation() {
  try {
    // 可能会失败的操作
    const result = riskyOperation()
    return result
  } catch(error) {
    // 手动上报错误
    Sentry.captureException(error)
    
    // 添加上下文信息
    Sentry.withScope(scope => {
      scope.setTag('operation', 'riskyOperation')
      scope.setUser({ id: 'user123' })
      scope.setExtra('additionalData', { input: '...' })
      Sentry.captureException(error)
    })
    
    // 向用户显示友好错误
    return { error: '操作失败，请稍后重试' }
  }
}
</script>
```

## 错误级别区分

Sentry监控会根据错误类型自动设置不同的严重级别：

- **HTTP/网络/超时错误**: `Sentry.Severity.Error`
- **业务错误**: `Sentry.Severity.Warning`

业务错误还会添加特殊标签`businessCode`，方便在Sentry后台过滤查询。

## 独立使用HTTP监控

如果需要单独使用HTTP错误监控（不使用Vue插件），可以：

```typescript
import { initSentryHttpMonitoring } from '@/utils/sentry/sentry'

// 确保先初始化了Sentry核心
import * as Sentry from '@sentry/browser'
Sentry.init({ dsn: 'https://<EMAIL>/project' })

// 然后初始化HTTP监控
const { uninstall } = initSentryHttpMonitoring({
  // 配置选项
  ignoreUrls: [/\/health$/],
  tags: { module: 'api-client' }
})

// 在不需要时可以卸载监控
// uninstall()
```

## 在SSR环境中使用

在服务端渲染环境中使用时需要注意：

```typescript
// main.ts
import { createSSRApp } from 'vue'
import SentryPlugin from '@/utils/sentry'
import App from './App.vue'

export function createApp() {
  const app = createSSRApp(App)
  
  // 仅在客户端初始化Sentry
  if (typeof window !== 'undefined') {
    app.use(SentryPlugin, {
      dsn: 'https://<EMAIL>/project',
      // 其他配置...
    })
  }
  
  return { app }
}
```

## 性能优化

Sentry集成支持性能追踪功能，可以帮助监控前端性能：

```typescript
import { createApp } from 'vue'
import router from './router'
import SentryPlugin from '@/utils/sentry'
import App from './App.vue'

const app = createApp(App)

app.use(SentryPlugin, {
  dsn: 'https://<EMAIL>/project',
  router,
  tracesSampleRate: 0.1, // 仅对10%的用户会话进行性能追踪
})

app.mount('#app')
```

### 手动创建性能追踪

```typescript
import { inject } from 'vue'

export function useComplexOperation() {
  const Sentry = inject('sentry')
  
  const performOperation = async (data) => {
    // 创建一个性能追踪跨度
    const transaction = Sentry.startTransaction({
      name: 'complex-data-processing',
    })
    
    try {
      // 第一步操作
      const span1 = transaction.startChild({ op: 'parse', description: '解析数据' })
      const parsedData = parseData(data)
      span1.finish()
      
      // 第二步操作
      const span2 = transaction.startChild({ op: 'transform', description: '转换数据' })
      const result = await transformData(parsedData)
      span2.finish()
      
      return result
    } finally {
      // 完成整个事务
      transaction.finish()
    }
  }
  
  return { performOperation }
}
```

## 用户上下文跟踪

可以设置用户上下文，帮助错误归因：

```typescript
import { useUserStore } from '@/stores/user'
import { inject, watch } from 'vue'

export function setupSentryUser() {
  const Sentry = inject('sentry')
  const userStore = useUserStore()
  
  // 监听用户状态变化，更新Sentry用户上下文
  watch(() => userStore.user, (user) => {
    if (user && user.id) {
      Sentry.setUser({
        id: user.id,
        username: user.name,
        email: user.email
      })
    } else {
      // 用户退出登录时清除上下文
      Sentry.setUser(null)
    }
  }, { immediate: true })
}
``` 