<template>
  <!-- 侧边栏遮罩层 -->
  <Transition name="sidebar">
    <div 
      v-if="internalVisible"
      class="fixed inset-0 z-50"
    >
      <!-- 遮罩背景 -->
      <div 
        class="absolute inset-0 bg-black bg-opacity-50 transition-opacity duration-300"
        @click="handleClose"
      ></div>
      
      <!-- 侧边栏内容 -->
      <div 
        class="absolute left-0 top-0 z-10 w-265px h-full bg-white"
        @click.stop
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
        <!-- 右侧圆角装饰 -->
        <div class="absolute top-0 right-0 w-12px h-full bg-white rounded-r-12px"></div>
        
        <!-- 侧边栏主体内容 -->
        <div class="px-15px pt-32px flex flex-col gap-16px h-full">
          <!-- 用户信息区域 -->
          <div class="flex items-center gap-8px">
            <!-- 用户头像 -->
            <div class="relative w-40px h-40px rounded-20px overflow-hidden">
              <img 
                v-if="userInfo?.headimgurl" 
                :src="userInfo.headimgurl" 
                :alt="userInfo.nickname"
                class="w-full h-full object-cover"
              />
              <div v-else class="w-full h-full bg-gray-200 flex items-center justify-center">
                <div class="w-20px h-20px bg-gray-400 rounded-full"></div>
              </div>
            </div>

            <!-- 用户信息文字 -->
            <div class="flex flex-col gap-2px">
              <div class="flex flex-col gap-6px">
                <div class="text-16px font-400 text-[#1C1C1C] leading-[1.4] text-center">
                  {{ userInfo?.nickname || '用户昵称' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 游戏切换区域 -->
          <div class="flex-1 flex flex-col gap-12px">
            <!-- 标题和描述 -->
            <div class="flex flex-col gap-2px">
              <div class="text-16px font-500 text-[#1C1C1C] leading-[1.4]">
                切换游戏
              </div>
              <div class="text-12px font-400 text-[#7E7E7E] leading-[1.4]">
                绑定其他游戏获得更多福利
              </div>
            </div>

            <!-- 游戏列表容器 -->
            <div class="flex-1 flex flex-col min-h-0">
              <!-- 游戏列表 -->
              <div class="flex flex-col gap-0 flex-1 overflow-y-auto">
                <div 
                  v-for="game in boundGamesWithInfo" 
                  :key="game.gameId"
                  class="flex items-center justify-between px-8px py-8px bg-[#EEEEEE] rounded-8px cursor-pointer hover:bg-gray-200 transition-colors mb-2px"
                  @click="handleGameClick(game)"
                >
                  <!-- 左侧游戏信息 -->
                  <div class="flex items-center gap-4px">
                    <!-- 游戏图标容器 -->
                    <div class="relative w-32px h-32px">
                      <img 
                        v-if="game.iconLogo" 
                        :src="game.iconLogo" 
                        :alt="game.name"
                        class="w-30.6px h-30.6px rounded object-cover absolute top-2.7px left-2.7px"
                      />
                      <div v-else class="w-30.6px h-30.6px bg-gray-300 rounded absolute top-2.7px left-2.7px"></div>
                    </div>

                    <!-- 游戏名称 -->
                    <div class="text-14px font-400 text-[#1C1C1C] leading-[1.4] text-center">
                      {{ game.name || '未知游戏' }}
                    </div>
                  </div>

                  <!-- 右侧激活状态 -->
                  <div class="w-16px h-16px flex items-center justify-center">
                    <div 
                      v-if="game.isActive"
                      class="w-16px h-16px bg-[#D3FF55] rounded-full flex items-center justify-center"
                    >
                      <!-- 对勾图标 -->
                      <div class="w-14px h-14px flex items-center justify-center">
                        <svg viewBox="0 0 16 16" class="w-7.5px h-5.21px">
                          <path d="M4.25 8.5L7 11.25L11.75 6.5" stroke="#1C1C1C" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 其他游戏按钮 -->
              <div class="mt-12px pb-32px">
                <button 
                  @click="handleAddGame" 
                  class="w-full h-44px bg-[#D3FF55] hover:bg-[#C8FF40] rounded-44px flex items-center justify-center gap-8px shadow-[0px_1px_4px_0px_rgba(0,0,0,0.15)] transition-colors cursor-pointer"
                >
                  <!-- 加号图标 -->
                  <div class="w-16px h-16px flex items-center justify-center">
                    <svg viewBox="0 0 16 16" class="w-13.44px h-13.44px">
                      <path d="M8 1.28v13.44M1.28 8h13.44" stroke="#1C1C1C" stroke-width="0.94" stroke-linecap="round"/>
                    </svg>
                  </div>
                  <div class="text-16px font-400 text-[#1C1C1C] leading-[1.4] text-center">
                    其他游戏
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useGameStore } from '@/stores/game'
import { useUserStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'
import type { Game } from '@/stores/game'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'game-switched'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const router = useRouter()
const gameStore = useGameStore()
const userStore = useUserStore()
const { showToast } = useToast()

// 内部可见性状态
const internalVisible = ref(props.visible)

// 监听外部visible变化
watch(() => props.visible, (newValue) => {
  internalVisible.value = newValue
})

// 用户信息
const userInfo = computed(() => userStore.wechatUserInfo)

// 已绑定的游戏列表（去重）
const boundGamesWithInfo = computed(() => {
  const activeGameId = gameStore.activeRole?.gameId
  
  return gameStore.boundGames.map(game => ({
    ...game,
    isActive: game.gameId === activeGameId
  }))
})

// 触摸相关状态
const touchStartX = ref(0)
const touchStartY = ref(0)
const touchEndX = ref(0)
const touchEndY = ref(0)
const isSwiping = ref(false)

// 处理触摸开始
const handleTouchStart = (event: TouchEvent) => {
  const touch = event.touches[0]
  touchStartX.value = touch.clientX
  touchStartY.value = touch.clientY
  isSwiping.value = false
}

// 处理触摸移动
const handleTouchMove = (event: TouchEvent) => {
  const touch = event.touches[0]
  const currentX = touch.clientX
  const currentY = touch.clientY
  
  const deltaX = currentX - touchStartX.value
  const deltaY = Math.abs(currentY - touchStartY.value)
  
  // 判断是否为水平滑动（水平距离大于垂直距离且水平距离大于阈值）
  if (Math.abs(deltaX) > deltaY && Math.abs(deltaX) > 10) {
    isSwiping.value = true
    // 阻止默认滚动行为
    event.preventDefault()
  }
}

// 处理触摸结束
const handleTouchEnd = (event: TouchEvent) => {
  if (!isSwiping.value) return
  
  const touch = event.changedTouches[0]
  touchEndX.value = touch.clientX
  
  const deltaX = touchEndX.value - touchStartX.value
  const minSwipeDistance = 50 // 最小滑动距离
  
  // 如果从右向左滑动且距离足够，则关闭侧边栏
  if (deltaX < -minSwipeDistance) {
    handleClose()
  }
  
  isSwiping.value = false
}

// 处理游戏点击
const handleGameClick = async (game: Game & { isActive: boolean }) => {
  if (game.isActive) {
    // 如果点击的是当前激活游戏，直接关闭侧边栏
    handleClose()
    return
  }

  try {
    // 获取该游戏下的第一个角色
    const gameuserGameList = gameStore.getuserGameListByGameId(game.gameId)
    if (gameuserGameList.length > 0) {
      const firstBinding = gameuserGameList[0]
      await gameStore.setActiveRole(firstBinding)
      showToast(`已切换到 ${game.name}`)
      emit('game-switched')
      handleClose()
    } else {
      showToast('该游戏下没有绑定的角色')
    }
  } catch (error) {
    console.error('游戏切换失败:', error)
    showToast('游戏切换失败，请重试')
  }
}

// 处理添加游戏
const handleAddGame = () => {
  router.push('/game/list')
  handleClose()
}

// 关闭侧边栏
const handleClose = () => {
  internalVisible.value = false
  emit('update:visible', false)
}
</script>

<style scoped>
/* 侧边栏动画 */
.sidebar-enter-active,
.sidebar-leave-active {
  transition: all 0.3s ease;
}

.sidebar-enter-from {
  opacity: 0;
}

.sidebar-enter-to {
  opacity: 1;
}

.sidebar-leave-from {
  opacity: 1;
}

.sidebar-leave-to {
  opacity: 0;
}

/* 侧边栏内容动画 */
.sidebar-enter-active > div:last-child {
  animation: sidebarSlideIn 0.3s ease-out;
}

.sidebar-leave-active > div:last-child {
  animation: sidebarSlideOut 0.3s ease-in;
}

@keyframes sidebarSlideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes sidebarSlideOut {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 4px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #F0F0F0;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #D0D0D0;
  border-radius: 2px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #B0B0B0;
}
</style> 