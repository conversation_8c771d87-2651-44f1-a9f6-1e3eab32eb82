<!-- GameList组件 - 合并GameCard和GameGrid功能 -->
<script setup lang="ts">
// 定义props
defineProps({
  gameList: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const handleGameClick = (game: any) => {
  console.log('点击游戏:', game)
}
</script>

<template>
  <div class="recharge-game-list">
    <h3 class="text-lg font-medium mb-4">热门游戏</h3>
    
    <!-- 游戏列表 -->
    <div class="game-grid grid grid-cols-3 gap-4 w-full">
      <!-- 显示游戏数据 -->
      <div class="grid-cell flex justify-center items-start min-h-34" v-for="game in gameList" :key="game.id">
        <div class="game-card w-28 h-30 relative flex flex-col items-center " @click="handleGameClick(game)">
          <!-- 游戏图片 -->
          <div class="game-image w-22 h-22 rounded-lg overflow-hidden relative">
            <!-- 游戏标签 -->
            <div class="game-tag absolute -top-4 right-0 " v-if="game.tagUrl">
              <img 
                :src="game.tagUrl" 
                alt="tag" 
                class=" float-right w-20 h-6"
              >
            </div>

            <img 
              :src="game.imageUrl" 
              :alt="game.name" 
              class="w-full h-full object-cover"
            >
          </div>
          
          <!-- 游戏名称 -->
          <div class="game-name text-center text-md truncate w-full">
            {{ game.name }}
          </div>
          
          <!-- 最近游玩标记 -->
          <div class="recent-badge text-xs bg-primary px-2 absolute left-1/2 -translate-x-1/2 bottom-8 h-4 w-10 text-center rounded-full" v-if="game.isRecent">
            最近
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.game-card {
  transition: transform 0.2s ease;
}

.game-card:active {
  transform: scale(0.98);
}
</style> 