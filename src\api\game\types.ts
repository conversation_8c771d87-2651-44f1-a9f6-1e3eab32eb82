import type { ApiResponse as HttpApiResponse } from '@/utils/http'

// 绑定信息项
export interface BindingItem {
  roleId: string
  roleName: string
  avatar: string
  gameId: string
  gameName: string
  isActive: boolean
  bindingId: number
  roleIdShow: string
}

// 获取绑定信息响应
export interface GetuserGameListResponse {
  status: string
  data: BindingItem[]
  code: number
  message: string
}

// 解绑游戏请求参数
export interface UnbindGameParams {
  bindingId: number
}

// 解绑游戏响应
export interface UnbindGameResponse {
  status: string
  data: string
  code: number
  message: string
}

// 切换活跃角色请求参数
export interface SwitchActiveGameParams {
  bindingId: number
}

// 切换活跃角色响应
export interface SwitchActiveGameResponse {
  status: string
  data: string
  code: number
  message: string
}
