import { ref, computed } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { ticketApi } from '@/api'
import type { Ticket } from '@/api/contact/types'
import { useToast } from '@/composables/useToast'

/**
 * 工单详情相关Hook
 */
export function useTicketDetail(ticketId?: string) {
  const toast = useToast()
  const id = ref(ticketId || '')
  
  // 获取工单详情
  const {
    data: ticketDetailData,
    loading: ticketDetailLoading,
    run: fetchTicketDetail
  } = useApiRequest(
    () => ticketApi.getTicketDetail(id.value),
    {
      manual: true
    }
  )
  
  // 催促工单处理
  const {
    data: urgeResultData,
    loading: urgeLoading,
    run: urgeTicket
  } = useApiRequest(
    (ticketId: string) => ticketApi.urgeTicket(ticketId),
    {
      manual: true,
      onSuccess: (response) => {
        if (response.data.success) {
          toast.success('催促已发送，客服将尽快处理')
        } else {
          toast.warning(response.data.message || '催促失败，请稍后再试')
        }
      },
      onError: () => {
        toast.error('网络错误，请稍后再试')
      }
    }
  )
  
  // 设置工单ID
  const setTicketId = (newId: string) => {
    id.value = newId
  }
  
  // 加载工单详情
  const loadTicketDetail = async () => {
    if (!id.value) {
      toast.warning('工单ID不能为空')
      return null
    }
    
    try {
      const result = await fetchTicketDetail()
      return result
    } catch (error) {
      console.error('获取工单详情失败', error)
      return null
    }
  }
  
  return {
    ticketDetail: computed(() => ticketDetailData.value?.data || null),
    loading: ticketDetailLoading,
    urgeLoading,
    setTicketId,
    loadTicketDetail,
    urgeTicket
  }
} 