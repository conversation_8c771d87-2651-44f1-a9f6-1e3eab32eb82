import { defineStore } from 'pinia'
import { useUserStore } from './user'


export const useSubscribeStore = defineStore('subscribe', {
  state: () => ({
    isSubscribed: false,
    lastCheckTime: null as number | null,
  }),

  actions: {
    async checkSubscribeStatus() {
      // try {
      //   const { wechatInfo } = useUserStore()
      //   wechatInfo.nickname = result.nickname
      //   wechatInfo.headimgurl = result.headimgurl
      //   this.isSubscribed = result.is_subscribe
      //   return this.isSubscribed
      // }
      // catch (error) {
      //   console.error('检查关注状态失败:', error)
      //   return false
      // }
    },

    setLastCheckTime(time: number) {
      this.lastCheckTime = time
    },
  },
})
