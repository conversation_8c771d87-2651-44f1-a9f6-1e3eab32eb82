import * as Sentry from '@sentry/browser';

interface SentryHttpOptions {
  // 是否启用Sentry上报
  enabled: boolean;
  // 忽略的URL正则表达式数组
  ignoreUrls?: RegExp[];
  // 忽略的错误状态码
  ignoreStatuses?: number[];
  // 忽略的业务错误码
  ignoreBusinessCodes?: number[];
  // 采样率 (0-1.0), 1表示全部上报
  sampleRate?: number;
  // 自定义标签
  tags?: Record<string, string>;
  // 是否在控制台也打印错误
  logToConsole?: boolean;
}

// 默认配置
export const defaultSentryOptions: SentryHttpOptions = {
  enabled: true,
  ignoreUrls: [/\/ping$/, /\/health$/, /\/heartbeat$/],
  ignoreStatuses: [404, 405],
  ignoreBusinessCodes: [],
  sampleRate: 1.0,
  logToConsole: process.env.NODE_ENV !== 'production',
};

/**
 * 初始化Sentry HTTP错误监控
 * @param options 配置选项
 */
export function initSentryHttpMonitoring(options: Partial<SentryHttpOptions> = {}) {
  const config = { ...defaultSentryOptions, ...options };
  
  if (!config.enabled || !Sentry.getCurrentHub().getClient()) {
    return { uninstall: () => {} };
  }
  
  // 判断是否应该忽略URL
  const shouldIgnoreUrl = (url: string): boolean => {
    if (!config.ignoreUrls?.length) return false;
    return config.ignoreUrls.some(pattern => pattern.test(url));
  };
  
  // 判断是否应该忽略HTTP状态码
  const shouldIgnoreStatus = (status?: number): boolean => {
    if (!status || !config.ignoreStatuses?.length) return false;
    return config.ignoreStatuses.includes(status);
  };
  
  // 判断是否应该忽略业务错误码
  const shouldIgnoreBusinessCode = (code?: number): boolean => {
    if (!code || !config.ignoreBusinessCodes?.length) return false;
    return config.ignoreBusinessCodes.includes(code);
  };
  
  // 判断是否应该基于采样率上报
  const shouldSample = (): boolean => {
    return Math.random() <= (config.sampleRate || 1.0);
  };
  
  // 处理HTTP错误
  const handleHttpError = (event: HttpErrorEvent) => {
    if (shouldIgnoreUrl(event.url) || 
        shouldIgnoreStatus(event.status) || 
        !shouldSample()) {
      return;
    }
    
    // 准备Sentry上下文
    const extraContext = {
      url: event.url,
      method: event.method,
      timestamp: new Date(event.timestamp).toISOString(),
      requestKey: event.requestKey,
      status: event.status,
      errorType: event.errorType,
    };
    
    // 设置自定义标签
    const tags = {
      httpMethod: event.method,
      errorType: event.errorType,
      ...(config.tags || {})
    };
    
    if (event.status) {
      tags.statusCode = String(event.status);
    }
    
    if (config.logToConsole) {
      console.error('[HTTP Error]', event.errorType, event.message, extraContext);
    }
    
    // 上报到Sentry
    Sentry.withScope(scope => {
      scope.setLevel(Sentry.Severity.Error);
      scope.setTags(tags);
      scope.setExtras(extraContext);
      
      // 如果有原始错误对象，直接上报
      if (event.error instanceof Error) {
        Sentry.captureException(event.error);
      } else {
        // 否则创建一个新错误对象
        const error = new Error(`HTTP ${event.errorType}: ${event.message}`);
        Sentry.captureException(error);
      }
    });
  };
  
  // 处理业务错误
  const handleBusinessError = (event: HttpErrorEvent) => {
    if (shouldIgnoreUrl(event.url) || 
        shouldIgnoreBusinessCode(event.code) || 
        !shouldSample()) {
      return;
    }
    
    // 业务错误特有的上下文
    const extraContext = {
      url: event.url,
      method: event.method,
      timestamp: new Date(event.timestamp).toISOString(),
      businessCode: event.code,
      errorType: 'business',
    };
    
    // 设置业务错误标签
    const tags = {
      httpMethod: event.method,
      errorType: 'business',
      businessCode: String(event.code),
      ...(config.tags || {})
    };
    
    if (config.logToConsole) {
      console.error('[Business Error]', event.code, event.message, extraContext);
    }
    
    // 上报到Sentry
    Sentry.withScope(scope => {
      scope.setLevel(Sentry.Severity.Warning); // 业务错误默认为警告级别
      scope.setTags(tags);
      scope.setExtras(extraContext);
      
      const error = new Error(`Business Error [${event.code}]: ${event.message}`);
      Sentry.captureException(error);
    });
  };
  
  // 注册监听器
  
  // 返回卸载函数
  return {
    uninstall: () => {
    }
  };
} 