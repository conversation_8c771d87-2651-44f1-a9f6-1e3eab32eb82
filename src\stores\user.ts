import { defineStore } from 'pinia'
import { type UserInfo as AuthUserInfo } from '@/api/auth'

// 定义WechatInfo类型
export interface WechatInfo {
	nickname: string
	headimgurl: string
}

// 更新 UserInfo 定义，移除 openid，添加时间字段，unionid 改为必填
export interface UserInfo {
  nickname: string
  headimgurl: string
  unionid: string
  createTime: string
  updateTime: string
}

// 用户Store
export const useUserStore = defineStore('user', {
	state: () => ({
		// 微信用户基本信息
		wechatUserInfo: {
			nickname: '',
			headimgurl: '',
			unionid: '',
			createTime: '',
			updateTime: ''
		} as UserInfo,
	}),

	getters: {
		// 用户昵称
		nickname(): string {
			return this.wechatUserInfo.nickname
		},
		
		// 用户头像
		avatar(): string {
			return this.wechatUserInfo.headimgurl
		},
		
		// 用户是否有基本信息
		hasUserInfo(): boolean {
			return !!this.wechatUserInfo.nickname && !!this.wechatUserInfo.headimgurl
		}
	},

	actions: {
		// 设置用户信息
		setUserInfo(userInfo: Partial<UserInfo>) {
			this.wechatUserInfo = { ...this.wechatUserInfo, ...userInfo }
		},
		
		// 从Auth响应设置用户信息
		setUserInfoFromAuth(authUser: AuthUserInfo) {
			this.setUserInfo({
				nickname: authUser.nickname,
				headimgurl: authUser.headimgurl,
				unionid: authUser.unionid,
				createTime: authUser.createTime,
				updateTime: authUser.updateTime
			})
		},
		
		// 清除用户信息
		clearUserInfo() {
			this.wechatUserInfo = {
				nickname: '',
				headimgurl: '',
				unionid: '',
				createTime: '',
				updateTime: ''
			}
		}
	},

	persist: {
		enabled: true,
		storage: localStorage,
		key: '__giant__user__info__',
	},
})
