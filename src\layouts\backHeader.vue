<template>
	<div class="min-h-screen bg-white-1 flex flex-col">
		<var-app-bar fixed placeholder color="#F8F8F8" elevation="0">
			<template #left>
				<var-button color="transparent" text-color="black" round text @click="router.back">
					<div class="i-mdi-chevron-left w-6 h-6"></div>
				</var-button>

				<div v-if="title.image" class="h-8">
					<img :src="title.image" alt="title" class="w-full h-full object-cover">
				</div>
				<div v-if="title.text" class="text-lg font-bold text-dark-1 text-center line-height-6">{{ title.text }}</div>
			</template>
			
			<!-- 菜单区域 -->
			<template v-if="menuItems.length > 0" #right>
				<var-menu>
					<var-button color="transparent" text-color="black" round text>
						<div class="i-mdi-dots-vertical w-6 h-6" style="transform: rotate(90deg);"></div>
					</var-button>
					<template #menu>
						<var-cell v-for="item in menuItems" :key="item.to || item.label" ripple @click="handleMenuClick(item)">
							<div class="flex items-center">
								<div v-if="item.icon" :class="[`i-${item.icon}`, 'mr-2 w-5 h-5']"></div>
								{{ item.label }}
							</div>
						</var-cell>
					</template>
				</var-menu>
			</template>
		</var-app-bar>

		
		<router-view class="flex-1 bg-white-1" v-slot="{ Component }">
			<component :is="Component" @update-layout-menu="handleUpdateMenu" @update-layout-title="handleUpdateTitle" />
		</router-view>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const router = useRouter()
const route = useRoute()

// 菜单显示状态

// 菜单项数据
const menuItems = ref<Array<{
  label: string
  to?: string
  icon?: string
  onClick?: () => void
}>>([])

// 监听页面传递的菜单数据
const handleUpdateMenu = (value: any) => {
  menuItems.value = value
}
const handleMenuClick = (item: any) => {
  if (item.onClick) {
    return item.onClick()
  } else if (item.to) {
		router.push(item.to)
	}
}

const title = ref({})

const handleUpdateTitle = (value: any) => {
	// value中可能存储image地址string 或者title文字。
	title.value = value
}

watch(() => route.path, (newVal) => {
	menuItems.value = []
	title.value = ''
}, { deep: true })
</script>

<style scoped></style>
