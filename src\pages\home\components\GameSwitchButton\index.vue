<template>
  <div 
    class="flex items-center justify-center gap-2 px-2.5 py-1.5 bg-[#1C1C1C] rounded-[30px] cursor-pointer transition-all duration-200 ease-in-out select-none hover:bg-[#2A2A2A] hover:-translate-y-0.5 active:translate-y-0" 
    @click="handleClick"
  >
    <!-- 游戏切换按钮图标 -->
    <img 
      :src="gameIcon" 
      :alt="game?.name"
      class="w-4 h-4 rounded-full"
    />
    <!-- 游戏名称 -->
    <span class="text-white text-base font-normal leading-[1.4] whitespace-nowrap overflow-hidden text-ellipsis max-w-[120px]">
      {{ game?.name || '未知游戏' }}
    </span>
  </div>
</template>

<script setup lang="ts">
import type { Game } from '@/stores/game'
import gameIcon from '@/assets/images/icons/game-switch-icon.svg'

interface Props {
  game?: Game | null
}

interface Emits {
  (e: 'click'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleClick = () => {
  emit('click')
}
</script>