<script lang="ts" setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue'

interface Option {
  value: string | number
  label: string
}

const props = defineProps({
  modelValue: { type: [String, Number], default: '' },
  options: { type: Array as () => Option[], required: true },
  placeholder: { type: String, default: 'Select an option' },
})
const emit = defineEmits(['update:modelValue'])

const searchQuery = ref('')
const isActive = ref(false)
const selectedValue = ref(props.modelValue)
const selectContainer = ref<HTMLElement | null>(null)
const dropdownPosition = ref({ top: 0, left: 0, width: 0 })

// 根据选中的值显示对应的标签
const updateLabelFromValue = () => {
  if (selectedValue.value) {
    const selectedOption = props.options.find(option => option.value === selectedValue.value)
    if (selectedOption) {
      searchQuery.value = selectedOption.label
    }
  } else {
    searchQuery.value = ''
  }
}

// 初始化和值变化时更新标签
watch(() => props.modelValue, (newValue) => {
  selectedValue.value = newValue
  updateLabelFromValue()
}, { immediate: true })

const filteredOptions = computed(() =>
  props.options.filter(option =>
    option.label.toLowerCase().includes(searchQuery.value.toLowerCase()),
  ),
)

// 计算下拉菜单位置
function updateDropdownPosition() {
  if (!selectContainer.value) return
  
  const rect = selectContainer.value.getBoundingClientRect()
  dropdownPosition.value = {
    top: rect.bottom + window.scrollY,
    left: rect.left + window.scrollX,
    width: rect.width
  }
}

function toggleDropdown(event: Event) {
  event.stopPropagation()
  if (!isActive.value) {
    updateDropdownPosition()
  }
  isActive.value = !isActive.value
}

function openDropdown(event: Event) {
  event.stopPropagation()
  updateDropdownPosition()
  isActive.value = true
}

function closeDropdown() {
  isActive.value = false
  updateLabelFromValue() // 关闭时恢复选中值的标签
}

function selectOption(option: Option) {
  selectedValue.value = option.value
  emit('update:modelValue', option.value)
  searchQuery.value = option.label
  closeDropdown()
}

function clearInput(event: Event) {
  event.stopPropagation()
  searchQuery.value = ''
  selectedValue.value = null
  emit('update:modelValue', null)
}

// Close dropdown if clicked outside
function handleOutsideClick(event: Event) {
  if (selectContainer.value && !selectContainer.value.contains(event.target as Node)) {
    closeDropdown()
  }
}

// 窗口大小变化时更新位置
function handleResize() {
  if (isActive.value) {
    updateDropdownPosition()
  }
}

onMounted(() => {
  document.addEventListener('click', handleOutsideClick)
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleResize)
  updateLabelFromValue() // 初始化时设置标签
})

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleResize)
})
</script>

<template>
  <div ref="selectContainer" class="relative w-full my-2">
    <div 
      class="w-full py-[10px] px-4 text-sm bg-[#EDEDED] rounded-4 h-[40px] flex items-center justify-between cursor-pointer" 
      :class="{ 'border-[#FFD722] border': isActive }"
      @click="toggleDropdown"
    >
      <input
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        class="w-full bg-transparent border-none outline-none placeholder:text-[#B8B8B8] text-[#1C1C1C]"
        @focus="openDropdown"
        @click.stop
      >
      <div v-if="!searchQuery" class="text-[#B8B8B8] text-xs transform transition-transform" :class="{'rotate-180': isActive}">
        <svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 10l5 5 5-5z" fill="currentColor"/>
        </svg>
      </div>
      <var-button
        v-if="searchQuery"
        class="p-0 h-auto min-w-0 bg-transparent text-[#B8B8B8]"
        text
        size="mini"
        @click.stop="clearInput"
      >
        ✕
      </var-button>
    </div>
    
    <!-- 使用Teleport将下拉菜单传送到body最外层 -->
    <teleport to="body">
      <transition name="slide-fade">
        <div 
          v-show="isActive" 
          class="fixed z-50 bg-white rounded-4 shadow-md overflow-hidden mt-1"
          :style="{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            width: `${dropdownPosition.width}px`
          }"
        >
          <div 
            v-if="filteredOptions.length === 0" 
            class="p-3 text-center text-[#B8B8B8]"
          >
            暂无数据
          </div>
          <div 
            class="overflow-y-auto"
            :style="{ maxHeight: `${Math.min(filteredOptions.length, 5) * 40}px` }"
          >
            <div
              v-for="option in filteredOptions"
              :key="option.value"
              class="p-3 hover:bg-[#f5f5f5] cursor-pointer text-sm h-[40px] flex items-center"
              :class="{ 'bg-[#f5f5f5] text-[#FFD722]': option.value === selectedValue }"
              @click="selectOption(option)"
            >
              {{ option.label }}
            </div>
          </div>
        </div>
      </transition>
    </teleport>
  </div>
</template>

<style scoped>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.2s ease;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
