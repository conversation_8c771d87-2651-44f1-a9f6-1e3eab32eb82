# Microsoft Clarity 集成

基于`@microsoft/clarity`包的行为分析和热图工具，支持会话记录、热图分析和用户行为追踪。

## 安装

```bash
# 使用pnpm安装
pnpm add @microsoft/clarity
```

## 使用方式

Clarity提供了多种使用方式，可以根据需要选择：

1. **全局自定义指令**: 通过`v-track`直接在模板中使用
2. **组合式API**: 使用`useClarity()`组合式函数
3. **依赖注入**: 通过`inject('clarity')`获取服务
4. **全局属性**: 通过`$clarity`全局属性使用

## 1. 全局自定义指令

使用`v-track`指令可以极简追踪用户交互事件，无需任何额外代码：

```vue
<template>
  <!-- 最简用法：只需添加指令 -->
  <button v-track>点击我</button>
  
  <!-- 指定事件名称 -->
  <button v-track="'确认订单'">确认订单</button>
  
  <!-- 完整数据对象 -->
  <button v-track="{
    action: '重要操作',
    category: '订单管理',
    value: order.id
  }">
    确认订单
  </button>
</template>
```

### 预定义追踪模式

```vue
<template>
  <!-- 表单提交追踪 -->
  <form v-track:submit="'表单提交'" @submit.prevent="handleSubmit">
    <!-- 表单内容 -->
    <button type="submit">提交</button>
  </form>
  
  <!-- 鼠标悬停追踪 -->
  <div v-track:hover="'产品卡片'">
    <!-- 卡片内容 -->
  </div>
  
  <!-- 元素可见追踪 - 当用户滚动到可见区域时自动触发 -->
  <div v-track:view="'重要区域'">
    <!-- 区域内容 -->
  </div>
  
  <!-- 仅在生产环境触发 -->
  <button v-track.prod="'生产环境按钮'">
    <!-- 按钮内容 -->
  </button>
</template>
```

## 2. 组合式API (推荐)

使用组合式函数是最灵活的方式，尤其适合复杂组件和逻辑：

```vue
<script setup>
import { useClarity } from '@/composables/useClarity'

// 获取所有Clarity功能
const { 
  trackEvent, 
  setUserId, 
  pause, 
  resume, 
  isAvailable 
} = useClarity()

// 记录自定义事件
function handleAction(data) {
  trackEvent('custom_action', {
    actionId: data.id,
    source: 'user_interaction'
  })
}

// 自动处理组件生命周期
import { useTrackLifecycle } from '@/composables/useClarity'

// 追踪组件挂载和卸载
useTrackLifecycle('ProductPage', {
  props: () => ({ productId: props.id }) // 动态数据
})
</script>
```

## 3. 依赖注入

通过依赖注入系统获取Clarity服务：

```vue
<script setup>
import { inject } from 'vue'

// 通过依赖注入获取Clarity
const clarity = inject('clarity')

function handleImportantAction(data) {
  clarity?.trackEvent('important_action', {
    actionType: 'submit',
    itemId: data.id,
    category: data.category
  })
}
</script>
```

## 4. 全局属性

通过全局属性访问Clarity（适合选项式API）：

```vue
<script>
export default {
  methods: {
    trackAction() {
      this.$clarity.trackEvent('some_action', { 
        value: 123 
      })
    }
  }
}
</script>
```

## 路由自动跟踪

插件已配置自动追踪路由变化，每次路由变化时会自动记录`page_view`事件，无需任何额外代码。

## 高级配置

如需修改Clarity配置，编辑`src/plugins/clarity.ts`文件：

```typescript
// src/plugins/clarity.ts
app.use(ClarityPlugin, {
  projectId: import.meta.env.VITE_CLARITY_PROJECT_ID || '',
  productionOnly: true,
  config: {
    // NPM包支持的配置项
    sessionSampleRate: 1, // 会话采样率
    urlSanitizer: (url) => url, // URL清理函数
    cookies: false, // 禁用cookies
    lean: true, // 轻量模式
    
    // 延迟初始化
    delay: 1500
  }
})
```

## 数据隐私

Microsoft Clarity不会收集用户的敏感信息：

- 不会收集用户输入的数据
- 会自动掩盖密码字段
- 可以通过CSS类(clarity-hidden)隐藏敏感元素
- 支持通过配置禁用cookies

## 敏感元素处理

在需要保护的元素上添加`clarity-hidden`类可以在记录中屏蔽该元素：

```html
<div class="clarity-hidden">
  <p>这段内容不会被Clarity记录</p>
  <span>敏感信息将被屏蔽</span>
</div>
```

## NPM包与脚本引入的区别

| 功能 | NPM包 | 脚本引入 |
|-----|-------|--------|
| 大小 | 更小，优化构建体积 | 完整脚本(~30KB) |
| 类型支持 | 完整TypeScript类型 | 无类型定义 |
| 配置方式 | 代码配置，类型安全 | 运行时配置 |
| 额外功能 | 支持暂停/恢复API | 基础功能集 |
| 构建集成 | 集成到构建流程 | 运行时加载 | 