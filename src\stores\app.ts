/* 页面全局状态与初始化调度 */
import { defineStore } from 'pinia'
import { useAuthStore } from './auth'
import { useUserStore } from './user'
import { useGameStore } from './game'
import { useThemeStore } from './theme'
import { configApi } from '@/api'
import type { GameBasic } from '@/api/config/types'

// 主题配置类型定义
interface HomeThemesConfig {
  emptyState: {
    background: string
    logo: string
    banners: Array<{
      id: number
      imageUrl: string
      jumpUrl?: string
      title?: string
    }>
  }
  gameThemes: {
    [gameId: number]: {
      background: string
      logo: string
      banners: Array<{
        id: number
        imageUrl: string
        jumpUrl?: string
        title?: string
      }>
    }
  }
}

export const useAppStore = defineStore('app', {
  state: () => ({
    /** 是否已完成首屏必需数据加载 */
    isReady: false,
    /** 当前是否正在初始化 */
    initializing: false,
  }),

  actions: {
    /**
     * 首屏初始化：
     * 1. 检查登录状态
     * 2. 并行拉取游戏列表、主题配置
     * 3. 委托game store获取用户绑定信息
     * 4. 更新对应 Store，再标记 ready
     */
    async init() {
      if (this.initializing || this.isReady) return
      this.initializing = true

      try {
        const authStore = useAuthStore()
        const gameStore = useGameStore()
        const themeStore = useThemeStore()

        // 1. 检查是否已登录，如果未登录则跳过数据加载
        if (!authStore.isLoggedIn || !authStore.hasValidToken) {
          console.log('[AppStore] 用户未登录，跳过数据初始化')
          this.isReady = true
          return
        }

        // 2. 并行获取基础配置数据
        const [gameBasicsRes, themeConfigRes] = await Promise.all([
          // 获取游戏基础配置
          configApi.queryConfig({
            configType: 'game',
            configKey: 'systemGameList',
          }),
          // 获取主题配置
          configApi.queryConfig({
            configType: 'system',
            configKey: 'homeThemes',
          }),
        ])

        // 3. 处理游戏基础配置
        if (gameBasicsRes.data && Array.isArray(gameBasicsRes.data)) {
          gameStore.setGames(gameBasicsRes.data)
        }

        // 4. 委托game store获取用户绑定信息
        await gameStore.loadUserGameList()

        // 5. 处理主题配置
        if (themeConfigRes.data) {
          const themeConfig = themeConfigRes.data as HomeThemesConfig
          themeStore.setHomeConfig(themeConfig)

          // 设置当前激活主题
          if (gameStore.activeRole) {
            themeStore.setActiveTheme(Number(gameStore.activeGameId))
          }
        }

        // 6. 标记就绪
        this.isReady = true
        console.log('[AppStore] 初始化完成')
      }
      catch (error) {
        console.error('[AppStore] 初始化失败:', error)

        // 检查是否是认证相关错误
        if (error?.code === 401001 || error?.code === 401002 || error?.status === 401) {
          console.log('[AppStore] 认证失效，将由路由守卫处理重新登录')
          // 认证错误由HTTP层和路由守卫自动处理，这里不需要特殊处理
        }

        // 即使失败也标记为就绪，避免阻塞页面
        this.isReady = true
      }
      finally {
        this.initializing = false
      }
    },

    /**
     * 强制重新初始化（用于登录后重新加载数据）
     */
    async forceInit() {
      this.reset()
      await this.init()
    },

    /**
     * 重置应用状态
     */
    reset() {
      this.isReady = false
      this.initializing = false
    }
  },
})