import type { RedirectRule } from '@/middleware/redirect'

/**
 * URL重定向规则配置
 * 
 * 配置结构：
 * - from: 原始路径（字符串或正则表达式）
 * - to: 目标路径
 * - preserveQuery: 是否保留查询参数（默认true）
 * - transformQuery: 自定义参数转换函数
 */
const redirectRules: RedirectRule[] = [
  // 例子：将 /a 重定向到 /b
  {
    from: '/a',
    to: '/b',
    preserveQuery: true
  },
  
  // 例子：将旧的游戏页面重定向到新结构
  {
    from: '/game-old',
    to: '/game/list',
    preserveQuery: true
  },
  
  // 例子：特定ID的映射，使用正则捕获组
  {
    from: /^\/product\/(\d+)$/,
    to: '/item/$1',
    preserveQuery: true
  },
  
  // 例子：参数名称映射转换
  {
    from: '/old-search',
    to: '/search',
    preserveQuery: true,
    transformQuery: (query) => ({
      keyword: query.q,
      category: query.cat,
      ...query
    })
  },
  
  // 在此处添加你的规则
  // ...
]

export default redirectRules 