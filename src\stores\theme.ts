import { defineStore } from 'pinia'
import { useGameStore } from './game'

// 服务项类型定义
export interface ServiceItem {
  id: number
  title: string
  description: string
  icon: string
  color?: string
}

// 轮播图类型定义
export interface BannerItem {
  id: number
  imageUrl: string
  jumpUrl?: string
  title?: string
}

// 主题类型定义
export interface ThemeConfig {
  background: string
  logo: string
  banners: BannerItem[]
}

// 首页配置类型
export interface HomeConfig {
  // 无状态主题 - 当用户没有绑定时显示
  emptyState: ThemeConfig
  // 游戏特定主题配置，key为gameId
  gameThemes: Record<number, ThemeConfig>
}

export const useThemeStore = defineStore('theme', {
  state: () => ({
    // 主页配置
    homeConfig: null as HomeConfig | null,
    // 当前激活主题ID，与游戏ID对应
    activeThemeId: 0
  }),

  getters: {
    /**
     * 根据游戏ID获取主题
     */
    getThemeByGameId: (state) => () => {
      const gameStore = useGameStore()
      const gameId = gameStore.activeGameId
      if (state.homeConfig && state.homeConfig.gameThemes[gameId]) {
        return state.homeConfig.gameThemes[gameId]
      }
    },

    /**
     * 获取无状态主题（用户未绑定时使用）
     */
    emptyStateTheme(): ThemeConfig {
      return this.homeConfig?.emptyState || {
        background: '',
        logo: '',
        banners: []
      }
    },
  },

  actions: {
    /**
     * 设置首页配置
     */
    setHomeConfig(config: HomeConfig) {
      this.homeConfig = config
      console.log('[ThemeStore] 设置主题配置成功', config)
    },

    /**
     * 设置当前主题ID
     */
    setActiveTheme(themeId: number) {
      this.activeThemeId = Number(themeId)
      console.log(`[ThemeStore] 切换到主题: ${themeId}`)
    },

    /**
     * 清除主题配置
     */
    clearThemeConfig() {
      this.homeConfig = null
      this.activeThemeId = 0
      console.log('[ThemeStore] 清除主题配置')
    }
  },

  persist: {
    enabled: true,
    storage: localStorage,
    key: '__giant__theme__config__',
  },
})