/* httpNew 事件总线 */
// 功能: 用 mitt 对外抛出请求阶段与错误相关事件，供 UI 或业务层解耦监听

import mitt from 'mitt'
import type { ApiResponse } from './types'

export interface RequestEvent {
  url: string
  method: string
  data?: unknown
  startAt: number
}

export interface SuccessEvent<T = any> extends RequestEvent {
  response: ApiResponse<T>
  duration: number
}

export interface ErrorEvent extends RequestEvent {
  /** 网络 / http / business / timeout */
  type: 'network' | 'http' | 'business' | 'timeout'
  status?: number
  code?: number
  message: string
  error?: unknown
}

export interface RetryEvent extends RequestEvent {
  /** 重试次数 */
  retryCount: number
  /** 重试原因 */
  reason: 'network' | 'timeout'
}

export interface AuthEvent {
  /** 是否刷新 Token 失败，需要重新登录 */
  relogin: boolean
}

export type HttpEventMap = {
  'request:start': RequestEvent
  'request:success': SuccessEvent
  'request:error': ErrorEvent
  'request:retry': RetryEvent
  'auth:logout': AuthEvent
}

export const httpEventBus = mitt<HttpEventMap>() 