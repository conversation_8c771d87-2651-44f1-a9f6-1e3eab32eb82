# HTTP 工具使用指南 - 重构版

## 概述

本项目提供了基于 axios 的简化 HTTP 客户端封装，重构后具有更清晰的架构和更简单的使用方式。

## 🚀 重构亮点

### 主要改进
- **统一认证控制**：使用直观的 `auth` 参数替代 `skipAuth`
- **简化 Token 管理**：移除复杂的双重保障机制，采用更健壮的单一策略
- **类似原生 axios**：提供更接近原生 axios 的使用体验
- **减少复杂度**：大幅简化事件系统和状态管理

## 核心特性

### 1. 简化的 Token 管理机制

#### 新的 Token 处理策略
- **请求前检查**：只在需要时进行基本的 token 有效性检查
- **失败时登出**：token 失效时直接触发登出，由路由守卫处理重新认证
- **避免过度刷新**：移除复杂的预检查机制，减少不必要的 token 刷新

#### 简化的处理流程
```typescript
1. 请求前检查
   ├─ 无需认证 (auth: false) → 直接发送请求
   ├─ 需要认证但无 token → 让请求失败，触发登出
   ├─ accessToken 有效 → 直接使用
   └─ accessToken 过期但 refreshToken 有效 → 刷新后使用

2. 请求失败处理
   ├─ Token 相关错误 → 直接登出
   └─ 其他错误 → 正常错误处理
```

### 2. 简化的事件系统

#### 保留的核心事件

```typescript
// 请求相关事件
'request:start'    // 请求开始
'request:success'  // 请求成功
'request:error'    // 请求错误
'request:retry'    // 请求重试

// 认证相关事件（简化）
'auth:logout'      // 用户登出（唯一的认证事件）
```

#### 移除的复杂事件
- `token:status` - 移除复杂的 token 状态事件
- 各种 token 刷新状态事件
- 过度细化的认证状态事件

### 3. 错误处理

#### 自动重试机制

- **网络错误**：自动重试，支持指数退避
- **超时错误**：自动重试，可配置重试次数
- **Token 失效**：自动刷新并重试请求

#### 业务错误处理

```typescript
// 配置业务错误码
business: {
  successCodes: [0],
  tokenInvalidCodes: [401001, 401002, 500002],
  codeMessage: {
    401001: 'Token无效',
    401002: 'Token已过期',
    500002: 'refresh_token无效'
  }
}
```

## 使用方法

### 1. 新的统一 API

```typescript
import { request, get, post, publicGet, publicPost } from '@/utils/http'

// 需要认证的请求（默认）
const userInfo = await get('/api/user/info')
const result = await post('/api/user/update', { name: 'John' })

// 使用 request 方法（类似原生 axios）
const response = await request({
  url: '/api/users',
  method: 'GET',
  params: { page: 1 },
  timeout: 5000
  // auth: true 是默认值，可省略
})

// 无需认证的请求
const config = await publicGet('/api/public/config')
const loginResult = await publicPost('/api/auth/login', { code: 'xxx' })

// 或者使用 auth 参数明确指定
const publicData = await request({
  url: '/api/public/data',
  method: 'GET',
  auth: false  // 明确指定无需认证
})
```

### 2. Token 监控

```typescript
import { useTokenMonitor } from '@/composables/useTokenMonitor'

// 在组件中使用
const { isRefreshing, lastTokenEvent, checkTokenStatus } = useTokenMonitor()

// 监听 Token 刷新状态
watch(isRefreshing, (refreshing) => {
  if (refreshing) {
    // 显示加载状态
    showLoading()
  } else {
    // 隐藏加载状态
    hideLoading()
  }
})
```

### 3. 事件监听

```typescript
import { httpEventBus } from '@/utils/http'

// 监听 Token 状态变化
httpEventBus.on('token:status', (event) => {
  console.log('Token 状态:', event.type)
  
  switch (event.type) {
    case 'refreshing':
      // 显示刷新提示
      break
    case 'refreshed':
      // 刷新成功
      break
    case 'refresh_failed':
      // 刷新失败，跳转登录
      break
  }
})

// 监听请求错误
httpEventBus.on('request:error', (event) => {
  console.error('请求错误:', event.message)
})
```

### 4. 自定义配置

```typescript
import { createHttp } from '@/utils/http'

// 创建自定义实例
const customHttp = createHttp({
  net: {
    baseURL: 'https://api.example.com',
    timeout: 10000
  },
  business: {
    successCodes: [0, 200],
    tokenInvalidCodes: [401, 403]
  },
  retry: {
    maxRetries: 3,
    retryDelay: 1000
  }
})
```

## 最佳实践

### 1. Token 管理

- ✅ **推荐**：使用请求前预检查机制
- ✅ **推荐**：监听 Token 状态事件进行 UI 反馈
- ❌ **避免**：手动调用刷新接口
- ❌ **避免**：在业务代码中直接处理 Token 过期

### 2. 错误处理

- ✅ **推荐**：使用事件系统统一处理错误
- ✅ **推荐**：配置合适的重试策略
- ❌ **避免**：在每个请求中重复处理错误

### 3. 性能优化

- ✅ **推荐**：使用共享 Promise 避免重复刷新
- ✅ **推荐**：合理配置超时时间
- ❌ **避免**：频繁创建 HTTP 实例

### 4. 调试技巧

```typescript
// 开启调试模式
import { httpEventBus } from '@/utils/http'

// 监听所有事件
httpEventBus.on('*', (event) => {
  console.log('HTTP Event:', event)
})

// 手动检查 Token 状态
const { checkTokenStatus } = useTokenMonitor()
checkTokenStatus()
```

## 配置说明

### 完整配置示例

```typescript
const config = {
  net: {
    baseURL: 'https://api.example.com',
    timeout: 15000,
    withCredentials: true
  },
  business: {
    successCodes: [0],
    tokenInvalidCodes: [401001, 401002, 500002],
    codeMessage: {
      401001: 'Token无效',
      401002: 'Token已过期'
    }
  },
  token: {
    headerKey: 'Authorization',
    prefix: 'Bearer '
  },
  retry: {
    maxRetries: 2,
    retryDelay: 1000,
    maxRetryDelay: 5000,
    retryableMethods: ['GET', 'HEAD', 'OPTIONS']
  },
  errorMessage: {
    default: '未知错误',
    network: '网络连接异常',
    timeout: '请求超时'
  }
}
```

## 迁移指南

### 从旧版本升级

1. **Token 刷新机制**：现在会自动在请求前检查，无需手动处理
2. **事件系统**：使用 `httpEventBus` 替代原有的回调方式
3. **错误处理**：统一通过事件系统处理，减少重复代码

### 兼容性

- 保持与现有 API 的兼容性
- 新增功能为可选，不影响现有代码
- 提供渐进式升级路径