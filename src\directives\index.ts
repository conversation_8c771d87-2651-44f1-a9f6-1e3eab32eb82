import type { App } from 'vue'
import trackDirective, { setupTrackDirectives } from './track'

// 导出所有指令，方便按需导入
export { trackDirective }

// 注册所有指令的函数
export function setupDirectives(app: App): void {
  // 注册v-track指令
  setupTrackDirectives(app)
  
  // 在这里添加其他指令的注册
  // 例如: setupOtherDirective(app)
  
  console.info('[自定义指令] 全局指令已注册')
}

// 默认导出安装函数，可在main.ts中直接使用
export default {
  install(app: App): void {
    setupDirectives(app)
  }
} 