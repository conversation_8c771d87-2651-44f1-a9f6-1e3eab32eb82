---
description: 前端性能优化的核心策略与实践。
globs: 
alwaysApply: false
---
# 前端性能优化规范

本规则旨在指导开发者关注并实践前端性能优化，以提升用户体验，核心围绕 Web Vitals 指标。
**MUST**: 在开发过程中始终考虑性能影响。

## 核心 Web Vitals 指标

*   **MUST**: 关注以下核心指标，力求达标：
    *   **LCP (Largest Contentful Paint)** < 2.5s (优良): 衡量**加载性能**。
        *   **关键优化点**: 优化关键渲染路径、图片优化、代码分割、资源加载优先级。
    *   **FID (First Input Delay)** < 100ms (优良) / **INP (Interaction to Next Paint)** < 200ms (优良): 衡量**交互响应性**。
        *   **关键优化点**: 减少主线程阻塞、优化 JavaScript 执行、事件处理防抖/节流。
    *   **CLS (Cumulative Layout Shift)** < 0.1 (优良): 衡量**视觉稳定性**。
        *   **关键优化点**: 图片/视频尺寸预设、避免动态插入无尺寸内容、字体加载策略。

## 加载性能优化 (LCP)

*   **代码分割 (Code Splitting)**:
    *   **MUST**: **利用 Vite 的默认路由懒加载**。页面组件会自动按需加载。
    *   **SHOULD**: 对于非首屏、体积较大或不立即需要的组件，使用 `defineAsyncComponent` 进行手动代码分割和异步加载。
        ```vue
        <script setup>
        import { defineAsyncComponent, ref } from 'vue'
        import MySkeleton from './MySkeleton.vue' // 骨架屏组件
        
        const HeavyComponent = defineAsyncComponent({
          loader: () => import('./HeavyComponent.vue'),
          loadingComponent: MySkeleton, // 加载时显示的组件
          delay: 200, // 延迟显示 loadingComponent (ms)
          // errorComponent: ErrorComponent, // 加载失败时显示的组件
          timeout: 3000 // 加载超时时间 (ms)
        });
        </script>
        <template>
          <HeavyComponent />
        </template>
        ```
    *   **SHOULD**: 结合 `<Suspense>` 使用异步组件，提供更好的加载状态管理 (但 `<Suspense>` 仍处于实验性阶段，谨慎使用)。
*   **图片优化**:
    *   **MUST**: 为图片（`<img>`, `<picture>`）和视频（`<video>`）设置明确的 `width` 和 `height` 属性，防止 CLS。
    *   **MUST**: 使用 `loading="lazy"` 对非首屏图片启用原生懒加载。
    *   **SHOULD**: 优先使用现代图片格式 (如 `WebP`, `AVIF`) 并提供兼容回退 (`<picture>` 标签)。
    *   **SHOULD**: 根据展示尺寸提供适当大小的图片，避免加载过大图片。
    *   **SHOULD**: 考虑使用 CDN 加速图片等静态资源加载。
*   **资源加载优先级**:
    *   **SHOULD**: 对首屏关键资源 (如 LCP 图片、核心 CSS/JS) 使用 `<link rel="preload">` 提升加载优先级。
    *   **SHOULD**: 对可能稍后需要的资源 (如下一个页面的资源) 使用 `<link rel="prefetch">` 进行预获取。
*   **字体优化**:
    *   **SHOULD**: 控制字体文件大小，使用 `woff2` 格式。
    *   **SHOULD**: 使用 `font-display: swap;` 或 `optional;` 策略，避免 FOIT/FOUT 导致的 CLS。
*   **减少关键渲染路径 (Critical Rendering Path) 资源**: 移除阻塞渲染的 CSS/JS，内联关键 CSS。

## 交互响应性优化 (FID / INP)

*   **减少主线程工作 (Long Tasks)**:
    *   **MUST**: 使用 `@vueuse/core` 的 `useDebounceFn` 或 `useThrottleFn` 处理高频触发的事件监听器 (如 `scroll`, `resize`, `input`)。
    *   **AVOID**: 在事件处理函数、`computed` 或 `watch` 中执行长时间运行的同步计算。
    *   **SHOULD**: 对复杂的、计算密集型任务，考虑使用 Web Workers 将其移出主线程。
*   **优化 JavaScript 执行**:
    *   **SHOULD**: 避免不必要的重绘 (Repaint) 和回流 (Reflow)。批量更新 DOM。
    *   **SHOULD**: 合理使用 `v-once` 指令缓存永不改变的 DOM 片段。
    *   **SHOULD**: 使用 `v-memo` 指令对大型列表或复杂组件进行条件性更新，避免不必要的 VNode diff。
        ```vue
        <div v-memo="[item.id]"> <!-- 仅当 item.id 变化时才重新渲染 -->
          <p>{{ item.name }}</p>
          <!-- ... 复杂的子结构 ... -->
        </div>
        ```
*   **虚拟列表 (Virtual Scrolling)**:
    *   **MUST**: 对于包含大量数据项的长列表 (成百上千条)，**必须** 使用虚拟滚动技术 (如 `vue-virtual-scroller`, `Varlet UI` 的 `List` 可能自带) 来渲染可见区域的列表项，避免一次性渲染大量 DOM 节点。

## 视觉稳定性优化 (CLS)

*   **MUST**: 为图片、视频、`<iframe>` 等嵌入内容预留空间 (设置 `width`/`height` 或使用 `aspect-ratio`)。
*   **AVOID**: 在现有内容上方动态插入内容，除非是用户交互触发的。
*   **SHOULD**: 确保 Web 字体加载不会导致布局大幅度变化 (使用 `font-display`, 字体预加载)。
*   **SHOULD**: 动画优先使用 CSS `transform` 和 `opacity`，避免触发布局变化的属性。

## 性能监测与分析

*   **MUST**: 定期使用工具分析应用性能：
    *   **浏览器 DevTools (Performance, Lighthouse)**: 分析运行时性能、识别长任务、检查 Web Vitals。
    *   **`vite-bundle-visualizer`**: 分析打包产物体积，找出过大的依赖项或模块。
    *   **真实用户监控 (RUM)**: (如 Sentry Performance) 监控线上用户的实际性能体验。