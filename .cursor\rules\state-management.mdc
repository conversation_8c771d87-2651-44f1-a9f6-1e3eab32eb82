---
description: Pinia store 的使用规范。
globs: 
alwaysApply: false
---
# 状态管理规范 (Pinia)

本规则 **强制** 规定使用 `Pinia` 进行全局或跨模块状态管理的规范。
**MUST**: 遵循以下规范定义和使用 Pinia Stores。

## 何时使用 Pinia

*   **MUST**: 当状态需要在**多个组件或页面**之间共享和响应式地访问时，使用 Pinia Store。
    *   示例：用户信息、购物车、全局应用设置、主题等。
*   **AVOID**: 将纯粹的本地组件状态或仅由 `useRequest` 管理的 API 数据放入 Pinia。
    *   对于 API 数据，优先使用 [API 交互规范 (api-interaction.mdc)](mdc:api-interaction.mdc) 中描述的 `useRequest` + `composables` 模式。
    *   对于仅限单组件的状态，使用 `ref` 或 `reactive`。

## Store 定义 (`src/stores/`)

*   **MUST**: 每个 Store **必须** 定义在 `src/stores/` 目录下，一个文件对应一个 Store。
*   **MUST**: Store 文件名和 ID **必须** 采用 `camelCase` 或 `kebab-case` (推荐 `camelCase`)，并以 `Store` 结尾，ID **必须** 是唯一的字符串。
    *   文件名: `userStore.ts`, `cartStore.ts`
    *   ID: `'user'`, `'cart'`
*   **MUST**: **必须** 使用 `defineStore` 的 `setup` 函数形式来定义 Store。
    ```typescript
    // src/stores/userStore.ts
    import { defineStore } from 'pinia'
    import { ref, computed } from 'vue'
    import { userApi } from '~/api/user/api' // 引入相关 API
    import type { UserProfile } from '~/api/user/types'
    // import { useRequest } from 'vue-request' // 可以在 Store 内部使用 useRequest

    export const useUserStore = defineStore('user', () => {
      // === State ===
      const profile = ref<UserProfile | null>(null); // 核心状态
      const token = ref<string | null>(localStorage.getItem('token')); // 可持久化状态
      const isLoadingProfile = ref(false); // Store 内部的加载状态

      // === Getters ===
      const isAuthenticated = computed(() => !!token.value && !!profile.value);
      const userId = computed(() => profile.value?.userId);

      // === Actions ===
      function setToken(newToken: string | null) {
        token.value = newToken;
        if (newToken) {
          localStorage.setItem('token', newToken);
        } else {
          localStorage.removeItem('token');
        }
      }

      function setProfile(newProfile: UserProfile | null) {
        profile.value = newProfile;
      }

      // Action 中可以包含异步操作
      async function fetchUserProfile() {
        if (!token.value) return; // 防御性编程
        isLoadingProfile.value = true;
        try {
          const res = await userApi.getUserProfile(); // 调用 API
          setProfile(res.data); // 假设 API 返回 { data: UserProfile }
        } catch (error) {
          console.error("Failed to fetch user profile:", error);
          // 清空或处理错误状态
          setProfile(null);
          setToken(null); // 可能需要登出
        } finally {
          isLoadingProfile.value = false;
        }
      }

      function logout() {
        setProfile(null);
        setToken(null);
        // 可能需要调用 API 通知后端
      }

      // === Initial Load (可选) ===
      // if (token.value) {
      //   fetchUserProfile(); // Pinia store 初始化时自动加载用户信息
      // }

      return {
        // State
        profile,
        token,
        isLoadingProfile,
        // Getters
        isAuthenticated,
        userId,
        // Actions
        setToken,
        setProfile,
        fetchUserProfile,
        logout,
      };
    });
    ```
*   **State**: 使用 `ref` 或 `reactive` 定义状态属性。
*   **Getters**: 使用 `computed` 定义派生状态。
*   **Actions**: 使用普通函数 (`function`) 定义，可以包含同步或异步逻辑，用于修改 State。
    *   **MUST**: 修改 State **必须** 在 Actions 中进行。
    *   **SHOULD**: Actions 中可以调用其他 Actions 或 API。

## 使用 Store

*   **MUST**: 在需要访问 Store 的组件或 Composable 中，调用 `useXxxStore()` 获取 Store 实例。
    ```vue
    <script setup lang="ts">
    import { computed } from 'vue' // 引入 computed (如果需要的话)
    import { useUserStore } from '~/stores/userStore'

    const userStore = useUserStore();

    // 直接访问 state (ref)
    // const profile = userStore.profile; // 不推荐直接解构，会失去响应性

    // 访问 getter (computed)
    const isLoggedIn = computed(() => userStore.isAuthenticated);
    const name = computed(() => userStore.profile?.nickname);

    // 调用 action (function)
    function handleLogout() {
      userStore.logout();
    }

    // 监听 state/getter 变化 (如果需要)
    watch(() => userStore.isAuthenticated, (isAuth) => {
      if (!isAuth) {
        // router.push('/login');
      }
    });
    </script>
    ```
*   **SHOULD**: 优先在 `<script setup>` 中使用 `computed` 包装从 Store 获取的值，然后在模板中使用该 `computed` 引用，以保持简洁和性能。
*   **AVOID**: 直接解构 Store 实例中的响应式状态 (`ref`, `reactive`, `computed`)，这会使其失去响应性。如果需要解构，使用 Pinia 提供的 `storeToRefs`。
    ```typescript
    import { storeToRefs } from 'pinia'
    const userStore = useUserStore()
    const { profile, isAuthenticated } = storeToRefs(userStore) // 正确：保持响应性
    ```

## 插件与持久化

*   **SHOULD**: 对于需要本地持久化的状态（如用户 Token），考虑使用 `pinia-plugin-persistedstate` 或手动在 Actions 中操作 `localStorage`/`sessionStorage`。
