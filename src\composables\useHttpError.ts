import { onUnmounted } from 'vue'
import { httpEventBus, type ErrorEvent, type RetryEvent } from '@/utils/http'
import { useToast } from '@/composables/useToast'

export interface HttpErrorHandler {
  /** 错误类型过滤 */
  type?: 'network' | 'http' | 'business' | 'timeout'
  /** 错误码过滤 */
  codes?: number[]
  /** 状态码过滤 */
  statusCodes?: number[]
  /** URL 匹配过滤 */
  urlPattern?: RegExp
  /** 自定义处理函数 */
  handler: (error: ErrorEvent) => void
}

export interface HttpRetryHandler {
  /** URL 匹配过滤 */
  urlPattern?: RegExp
  /** 重试原因过滤 */
  reason?: 'network' | 'timeout'
  /** 自定义处理函数 */
  handler: (retry: RetryEvent) => void
}

/**
 * HTTP 错误处理 Hook
 * 允许组件级别自定义错误处理逻辑
 */
export function useHttpError() {
  const toast = useToast()
  const cleanupHandlers: (() => void)[] = []

  /**
   * 注册错误处理器
   */
  const handleError = (config: HttpErrorHandler) => {
    const stopListener = httpEventBus.on('request:error', (error) => {
      // 类型过滤
      if (config.type && error.type !== config.type) return
      
      // 错误码过滤
      if (config.codes && error.code && !config.codes.includes(error.code)) return
      
      // 状态码过滤
      if (config.statusCodes && error.status && !config.statusCodes.includes(error.status)) return
      
      // URL 匹配过滤
      if (config.urlPattern && !config.urlPattern.test(error.url)) return
      
      // 执行自定义处理
      config.handler(error)
    })

    cleanupHandlers.push(stopListener)
    return stopListener
  }

  /**
   * 注册重试处理器
   */
  const handleRetry = (config: HttpRetryHandler) => {
    const stopListener = httpEventBus.on('request:retry', (retry) => {
      // 重试原因过滤
      if (config.reason && retry.reason !== config.reason) return
      
      // URL 匹配过滤
      if (config.urlPattern && !config.urlPattern.test(retry.url)) return
      
      // 执行自定义处理
      config.handler(retry)
    })

    cleanupHandlers.push(stopListener)
    return stopListener
  }

  /**
   * 快捷方法：处理特定业务错误码
   */
  const handleBusinessError = (codes: number[], handler: (error: ErrorEvent) => void) => {
    return handleError({
      type: 'business',
      codes,
      handler
    })
  }

  /**
   * 快捷方法：处理特定 URL 的错误
   */
  const handleUrlError = (urlPattern: RegExp, handler: (error: ErrorEvent) => void) => {
    return handleError({
      urlPattern,
      handler
    })
  }

  /**
   * 快捷方法：静默处理错误（不显示全局提示）
   */
  const silentError = (config: Omit<HttpErrorHandler, 'handler'>) => {
    return handleError({
      ...config,
      handler: () => {
        // 静默处理，不显示提示
      }
    })
  }

  /**
   * 快捷方法：自定义错误提示
   */
  const customErrorMessage = (config: Omit<HttpErrorHandler, 'handler'>, message: string) => {
    return handleError({
      ...config,
      handler: () => {
        toast.error(message)
      }
    })
  }

  /**
   * 快捷方法：监听重试事件并显示提示
   */
  const showRetryMessage = (config: Omit<HttpRetryHandler, 'handler'>, message?: string) => {
    return handleRetry({
      ...config,
      handler: (retry) => {
        const defaultMessage = `正在重试请求 (${retry.retryCount}/${retry.retryCount + 1})`
        toast.info(message || defaultMessage)
      }
    })
  }

  // 组件卸载时清理所有监听器
  onUnmounted(() => {
    cleanupHandlers.forEach(cleanup => cleanup())
  })

  return {
    handleError,
    handleRetry,
    handleBusinessError,
    handleUrlError,
    silentError,
    customErrorMessage,
    showRetryMessage
  }
}