<script setup lang="ts">
defineProps({
  swipeItems: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
})

const handleBannerClick = (item: any) => {
  console.log('点击轮播图:', item)
}
</script>

<template>
  <div class="text-swipe rounded-2xl overflow-hidden">
    <!-- 只处理有数据和无数据两种情况，不需要骨架图逻辑 -->
    <var-swipe v-if="swipeItems.length > 0" class="swipe-wrapper h-18">
      <var-swipe-item v-for="(item, index) in swipeItems" :key="index">
        <div 
          class="text-center h-full flex items-center justify-center" 
          @click="handleBannerClick(item)"
        >
          <img 
            v-if="item.imageUrl" 
            :src="item.imageUrl" 
            :alt="item.text || '广告'" 
            class="h-full object-cover w-full"
          >
          <div v-else class="flex items-center justify-center text-white h-full w-full bg-gray-700">
            {{ item.text || `内容 ${index + 1}` }}
          </div>
        </div>
      </var-swipe-item>

      <template #indicator="{ index, length, to }">
        <div class="absolute flex top-3 left-3">
          <div
            class="swipe-indicator w-1 h-1 rounded-full bg-primary opacity-30 mx-1 transition-opacity duration-300"
            v-for="(l, idx) in length"
            :key="l"
            :class="{ '!opacity-100': idx === index }"
            @click="to(idx)"
          ></div>
        </div>
      </template>
    </var-swipe>
    
    <!-- 没有数据显示空状态 -->
    <div v-else class="empty-swipe h-18 flex items-center justify-center rounded-2xl bg-gray-100">
      <span class="text-gray-400">暂无更多内容</span>
    </div>
  </div>
</template>

<style scoped>
.swipe-indicator {
  will-change: opacity;
}
</style> 