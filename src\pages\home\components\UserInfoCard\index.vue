<template>
  <div class="flex flex-col items-center justify-center gap-4">
    <!-- 头像容器 -->
    <div class="w-20 h-20 rounded-[39px] overflow-hidden shadow-[0px_1px_4px_0px_rgba(0,0,0,0.4)]">
      <img 
        v-if="userInfo.headimgurl" 
        :src="userInfo.headimgurl" 
        :alt="userInfo.nickname"
        class="w-full h-full object-cover"
      />
      <div v-else class="w-full h-full bg-[#333] flex items-center justify-center">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
          <path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="#666"/>
        </svg>
      </div>
    </div>
    
    <!-- 用户昵称 -->
    <div class="text-center">
      <div class="text-[#1C1C1C] text-base font-normal leading-[1.4] flex items-center justify-center gap-1">
        {{ userInfo.nickname || '微信用户' }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UserInfo } from '@/stores/user'

interface Props {
  userInfo: UserInfo
}

const props = defineProps<Props>()
</script>