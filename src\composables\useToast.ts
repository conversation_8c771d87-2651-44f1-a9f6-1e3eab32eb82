import { ref } from 'vue'
import 'vue-toastification/dist/index.css'
import { createToastInterface } from 'vue-toastification'

const toast = createToastInterface()

/**
 * Toast 通知 Hook
 * 提供统一的消息提示功能
 */
export function useToast() {
  const isVisible = ref(false) 

  const success = (message: string, options?: any) => {
    toast.success(message, options)
  }

  const error = (message: string, options?: any) => {
    toast.error(message, options)
  }

  const warning = (message: string, options?: any) => {
    toast.warning(message, options)
  }

  const info = (message: string, options?: any) => {
    toast.info(message, options)
  }

  const clear = () => {
    toast.clear()
  }

  return {
    success,
    error,
    warning,
    info,
    clear,
    isVisible
  }
} 