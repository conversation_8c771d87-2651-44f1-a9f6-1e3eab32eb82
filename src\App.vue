<template>
	<router-view />
</template>

<style>
/* a {
	color: rgba(37, 99, 235);
} */

p {
	padding: 0 10px;
}
</style>

<script setup lang="ts">
// # App.vue 首行可保留页面注释
import { onMounted, watch } from 'vue'
import { useAppStore, useAuthStore } from '@/stores'

const appStore = useAppStore()
const authStore = useAuthStore()

const tryInit = async () => {
  // 只有在已登录状态下才进行初始化
  if (authStore.loginStatus === 2 /* LOGGED_IN */) {
    console.log('[App] 开始初始化应用数据...')
    await appStore.init()
  } else {
    console.log('[App] 用户未登录，跳过初始化')
  }
}

onMounted(() => {
  console.log('[App] 应用挂载完成，开始初始化检查')
  tryInit()
})

// 当登录状态变化时，重新初始化
watch(() => authStore.loginStatus, (newStatus, oldStatus) => {
  console.log(`[App] 登录状态变化: ${oldStatus} -> ${newStatus}`)

  if (newStatus === 2 /* LOGGED_IN */) {
    // 登录成功，初始化数据
    tryInit()
  } else if (oldStatus === 2 /* LOGGED_IN */ && newStatus !== 2) {
    // 登出，重置应用状态
    console.log('[App] 用户登出，重置应用状态')
    appStore.reset()
  }
})
</script>
