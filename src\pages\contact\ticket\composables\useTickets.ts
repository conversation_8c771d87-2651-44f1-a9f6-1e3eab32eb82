import { ref, computed } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { ticketApi } from '@/api'
import type { Ticket } from '@/api/contact/types'

/**
 * 工单列表功能Hook
 */
export function useTickets() {
  // 获取工单列表
  const {
    data: ticketsData,
    loading,
    error,
    run: fetchTickets
  } = useApiRequest(
    () => ticketApi.getTickets(),
    {
      manual: false
    }
  )
  
  // 工单列表
  const tickets = computed<Ticket[]>(() => {
    return ticketsData.value?.data || []
  })
  
  // 按状态分组
  const groupedTickets = computed(() => {
    if (!tickets.value?.length) return { processing: [], completed: [] }
    
    return {
      processing: tickets.value.filter(t => t.status === 'processing'),
      completed: tickets.value.filter(t => t.status === 'completed')
    }
  })
  
  // 刷新工单列表
  const refreshTickets = () => {
    fetchTickets()
  }
  
  return {
    tickets,
    groupedTickets,
    loading,
    error,
    refreshTickets
  }
} 