import type { Directive, App } from 'vue'
import * as clarity from '@microsoft/clarity'

/**
 * 预定义的追踪模式
 */
export const TrackMode = {
  /** 追踪点击事件 */
  CLICK: 'click',
  /** 追踪输入事件 */
  INPUT: 'input',
  /** 追踪表单提交 */
  SUBMIT: 'submit',
  /** 追踪鼠标悬停 */
  HOVER: 'hover',
  /** 追踪元素可见 */
  VIEW: 'view'
}

/**
 * 自定义追踪指令
 * 
 * 基本用法:
 * <button v-track>按钮</button>
 * 
 * 指定事件名称:
 * <button v-track="'按钮点击'">按钮</button>
 * 
 * 完整事件数据:
 * <button v-track="{ action: '提交订单', orderId: order.id }">提交</button>
 * 
 * 使用预定义模式:
 * <form v-track:submit="'表单提交'">...</form>
 * <div v-track:hover="'产品卡片'">...</div>
 * 
 * 仅在生产环境启用:
 * <button v-track.prod>按钮</button>
 */
const trackDirective: Directive = {
  mounted(el, binding) {
    // 只在生产环境中处理prod修饰符
    if (binding.modifiers.prod && !import.meta.env.PROD) {
      return
    }
    
    // 处理预定义模式
    const arg = binding.arg || 'click'
    let eventName = arg
    
    // 映射自定义模式到实际DOM事件
    if (arg === TrackMode.HOVER) {
      eventName = 'mouseenter'
    } else if (arg === TrackMode.VIEW) {
      // 使用IntersectionObserver追踪元素可见性
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            // 元素进入可视区域时记录事件
            trackElement(el, 'element_view', binding.value)
            // 只触发一次
            observer.disconnect()
          }
        })
      }, { threshold: 0.5 }) // 元素至少50%可见时触发
      
      observer.observe(el)
      return
    }
    
    // 基础事件数据
    const prepareEventData = () => {
      // 创建基础数据对象
      const data: Record<string, any> = {
        timestamp: Date.now()
      }
      
      // 处理不同的值类型
      const value = binding.value
      if (typeof value === 'string') {
        data.action = value
      } else if (typeof value === 'object' && value !== null) {
        Object.assign(data, value)
      }
      
      // 自动获取元素文本作为标签(如果没有指定)
      if (!data.label && el.textContent) {
        data.label = el.textContent.trim().substring(0, 30)
      }
      
      // 自动获取元素类型
      if (!data.elementType) {
        data.elementType = el.tagName.toLowerCase()
      }
      
      return data
    }
    
    // 添加事件监听
    el.addEventListener(eventName, () => {
      trackElement(el, `ui_${eventName}`, binding.value)
    })
  }
}

// 工具函数：执行实际的追踪
function trackElement(el: HTMLElement, eventType: string, value: any) {
  // 创建基础数据对象
  const data: Record<string, any> = {
    timestamp: Date.now()
  }
  
  // 处理不同的值类型
  if (typeof value === 'string') {
    data.action = value
  } else if (typeof value === 'object' && value !== null) {
    Object.assign(data, value)
  }
  
  // 自动获取元素文本作为标签(如果没有指定)
  if (!data.label && el.textContent) {
    data.label = el.textContent.trim().substring(0, 30)
  }
  
  // 自动获取元素类型
  if (!data.elementType) {
    data.elementType = el.tagName.toLowerCase()
  }
  
  // 发送事件到Clarity
  clarity.event(eventType, data)
}

// 安装函数
export function setupTrackDirectives(app: App) {
  app.directive('track', trackDirective)
}

export default trackDirective 