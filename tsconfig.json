{"compilerOptions": {"strict": true, "jsx": "preserve", "target": "esnext", "module": "esnext", "sourceMap": true, "skipLibCheck": true, "isolatedModules": true, "jsxImportSource": "vue", "esModuleInterop": true, "lib": ["esnext", "dom"], "resolveJsonModule": true, "moduleResolution": "node", "useDefineForClassFields": true, "baseUrl": ".", "paths": {"~/*": ["src/*"], "@/*": ["src/*"]}}, "include": ["srcipts", "presets", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "./vite.config.ts"], "exclude": ["node_modules", "dist"]}