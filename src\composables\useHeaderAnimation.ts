import { ref, computed, watch, onMounted } from 'vue'
import { useScroll } from '@vueuse/core'

export interface HeaderAnimationOptions {
  // 基础配置
  expandedHeight?: number
  collapsedHeight?: number
  startCollapseThreshold?: number
  fullCollapseThreshold?: number
  
  // 边距计算方式
  marginTopMode?: 'home' | 'recharge' | 'custom'
  marginTopCustomValue?: number | ((height: number) => number)
  
  // 功能开关
  enableAvatarAnimation?: boolean
  checkContentHeight?: boolean
  
  // 性能优化选项
  throttle?: number
  ratioChangeThreshold?: number
}

export function useHeaderAnimation(options: HeaderAnimationOptions = {}) {
  // 合并默认配置
  const {
    expandedHeight = 144,
    collapsedHeight = 60,
    startCollapseThreshold = 30,
    fullCollapseThreshold = 80,
    marginTopMode = 'home',
    marginTopCustomValue = 0,
    enableAvatarAnimation = false,
    checkContentHeight = false,
    throttle = 16,
    ratioChangeThreshold = 0.01
  } = options
  
  // 滚动监听 - 添加passive选项提高性能
  const { y: scrollY } = useScroll(window, { 
    throttle,
    passive: true
  })
  
  const isHeaderCollapsed = ref(false)
  const hasEnoughContent = ref(true)
  
  // 存储最后一次计算的比率，防止频繁变化
  const lastRatio = ref(0)

  // 计算头部收缩状态和高度 - 增加稳定性处理
  const headerCollapseRatio = computed(() => {
    let ratio = 0
    
    if (scrollY.value <= startCollapseThreshold) {
      ratio = 0
    } else if (scrollY.value >= fullCollapseThreshold) {
      ratio = 1
    } else {
      const rawRatio = (scrollY.value - startCollapseThreshold) / (fullCollapseThreshold - startCollapseThreshold)
      ratio = Math.pow(rawRatio, 1.5) // 使用幂函数使动画更自然
    }
    
    // 比较差值，小变化忽略不计
    if (Math.abs(ratio - lastRatio.value) < ratioChangeThreshold) {
      return lastRatio.value
    }
    
    // 存储这次的比率
    lastRatio.value = ratio
    return ratio
  })

  // 精确计算头部高度，避免小数点导致的抖动
  const headerHeight = computed(() => {
    const currentHeight = expandedHeight - ((expandedHeight - collapsedHeight) * headerCollapseRatio.value)
    // 取整，避免小数导致的抖动
    return `${Math.floor(currentHeight)}px`
  })

  // 根据不同页面计算内容区域的上边距
  const marginTop = computed(() => {
    // 去除数据中的px
    const height = parseInt(headerHeight.value.replace('px', ''))
    
    // 根据不同页面使用不同的计算方式
    if (marginTopMode === 'home') {
      return `${height * 1.4}px`
    } else if (marginTopMode === 'recharge') {
      return `${height + 5}px`
    } else if (marginTopMode === 'custom') {
      if (typeof marginTopCustomValue === 'function') {
        return `${marginTopCustomValue(height)}px`
      }
      return `${height + marginTopCustomValue}px`
    }
    
    return headerHeight.value
  })

  // 计算avatar的位置样式 (仅在home页面使用)
  const avatarStyle = computed(() => {
    if (!enableAvatarAnimation) return {}
    
    // 垂直位移 - 优化动画表现
    const translateY = headerCollapseRatio.value > 0.5 ? 
      `-${30 + 20 * headerCollapseRatio.value}px` : 
      '0px'
      
    // 水平位移
    const translateX = headerCollapseRatio.value > 0.5 ? 
      `0px` : 
      '0px'
      
    // 缩放比例
    const scale = 1 - (headerCollapseRatio.value * 0.35)
    
    return {
      transform: `translate(${translateX}, ${translateY}) scale(${scale})`,
      opacity: isHeaderCollapsed.value ? 0 : 1,
      transition: 'transform 0.3s cubic-bezier(0.33, 1, 0.68, 1), opacity 0.2s ease',
      willChange: 'transform, opacity' // 性能优化
    }
  })

  // 检查页面内容是否足够支持滚动
  function checkPageContentHeight() {
    if (!checkContentHeight) return
    
    const pageHeight = document.documentElement.clientHeight
    const contentHeight = document.documentElement.scrollHeight
    
    // 页面内容是否足够触发滚动 - 增加更大的阈值
    hasEnoughContent.value = contentHeight > pageHeight + 100
  }

  // 监听滚动事件更新收缩状态
  watch(scrollY, (newValue) => {
    if (newValue > fullCollapseThreshold && !isHeaderCollapsed.value) {
      isHeaderCollapsed.value = true
    } else if (newValue < startCollapseThreshold && isHeaderCollapsed.value) {
      isHeaderCollapsed.value = false
    }
  }, { immediate: true })
  
  // 页面加载和窗口大小变化时检查内容高度
  onMounted(() => {
    if (checkContentHeight) {
      checkPageContentHeight()
      window.addEventListener('resize', checkPageContentHeight)
      
      // 内容可能是异步加载的，延迟再次检查
      setTimeout(checkPageContentHeight, 1000)
    }
  })

  return {
    scrollY,
    isHeaderCollapsed,
    headerCollapseRatio,
    headerHeight,
    marginTop,
    hasEnoughContent,
    avatarStyle
  }
}