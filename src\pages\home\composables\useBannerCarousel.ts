import { computed } from 'vue'
import { useRouter } from 'vue-router'

export interface BannerItem {
  id: number
  imageUrl: string
  jumpUrl?: string
  title?: string
}

export function useBannerCarousel(banners: BannerItem[]) {
  const router = useRouter()
  
  // 计算属性
  const hasBanners = computed(() => banners.length > 0)
  
  // 处理轮播点击
  const handleBannerClick = (banner: BannerItem) => {
    if (!banner.jumpUrl) return
    
    // 判断是内部链接还是外部链接
    if (banner.jumpUrl.startsWith('http')) {
      // 外部链接，新窗口打开
      window.open(banner.jumpUrl, '_blank')
    } else {
      // 内部链接，使用路由跳转
      router.push(banner.jumpUrl)
    }
  }
  
  return {
    // 状态
    hasBanners,
    
    // 方法
    handleBannerClick
  }
}